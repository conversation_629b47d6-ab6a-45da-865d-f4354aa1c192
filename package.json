{"name": "mev_bot", "version": "1.0.0", "description": "MEV Bot - Microservices Architecture", "workspaces": ["packages/*", "services/*", "apps/*"], "scripts": {"build:all": "npm run build --workspaces", "build:shared": "npm run build --workspace=packages/shared", "build:services": "npm run build --workspace=services/gateway --workspace=services/strategy --workspace=services/decision --workspace=services/trade --workspace=services/orders --workspace=services/chains", "dev:main": "npm run dev --workspace=apps/main", "dev:gateway": "npm run dev --workspace=services/gateway", "dev:strategy": "npm run dev --workspace=services/strategy", "dev:decision": "npm run dev --workspace=services/decision", "dev:trade": "npm run dev --workspace=services/trade", "dev:orders": "npm run dev --workspace=services/orders", "dev:chains": "npm run dev --workspace=services/chains", "start:gateway": "cd services/gateway && npm run start", "start:strategy": "cd services/strategy && npm run start", "start:decision": "cd services/decision && npm run start", "start:trade": "cd services/trade && npm run start", "start:orders": "cd services/orders && npm run start", "start:chains": "cd services/chains && npm run start", "dev:orders:prod": "NODE_ENV=production npm run dev:orders", "dev:orders:test": "NODE_ENV=test npm run dev:orders", "clean:all": "npm run clean --workspaces"}, "devDependencies": {"@types/node": "^20.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "dependencies": {"winston": "^3.17.0"}}