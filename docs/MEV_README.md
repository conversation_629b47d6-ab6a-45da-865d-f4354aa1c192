# 链上高频交易策略设计
共有以下几个模块构成

### 1. 网关模块

用于对接外部数据, 所有数据都需要统一从网关接入

### 2. 策略模块

每一笔交易都会经过策略组的处理变成一个可评估的分数

### 3. 订单中心模块

维护已有token, 交易等

### 4. 交易模块

买入卖出操作

### 5. 信息后置模块(可能暂时不需要)

用于买入卖出后的数据处理, 可能上报采集用于分析

## 流转流程

1. 每一笔交易都会经过网关转换成统一模型, 然后传递到策略组
2. 策略组处理后, 得到一个评分(这一部分是异步的? )
3. 评分完成后, 异步消息推送给决策模型, 决策后决定是否买入/卖出(买入多少, 卖出多少), 然后将交易信号推送给交易模块
4. 交易模块监听到信号后, 根据决策模型给的交易清单, 进行交易
5. 交易的时候需要查询下订单薄, 看是否有已有仓位, 来判断进行加仓, 减仓操作(高效内存db查询?)
6. 交易完成后, 将交易数据信息上报给采集端, 用于搜集信息训练后续决策模型

## 挑战&难点
1. 貔貅过滤(网关前置过滤? 如果)
2. 多对策略进行实际数据的回测
3. 每次进过策略组, 决策模型的速度快慢(也可以叫决策快慢?)




