# MEV交易系统架构流程图

## 一、整体架构流程图

```mermaid
graph TB
    subgraph "数据源层"
        BC1[EVM链节点]
        BC2[Solana节点]
        BC3[Sui节点]
        MS[Mempool监听]
        DS[DEX事件流]
    end

    subgraph "网关层"
        GW[统一网关服务]
        FT[貔貅过滤器]
        DM[数据模型转换器]
        RL[限流器]
    end

    subgraph "缓存层"
        RC[Redis缓存]
        MC[内存缓存]
        HP[热点数据池]
    end

    subgraph "策略层"
        SE[策略引擎]
        SM[聪明钱策略]
        SI[社交影响策略]
        PT[价格趋势策略]
        VS[交易量策略]
        SA[策略聚合器]
    end

    subgraph "决策层"
        DEC[决策模型]
        ML[机器学习模型]
        RS[风险评估]
        PS[仓位计算]
    end

    subgraph "订单管理"
        OB[订单簿]
        PM[持仓管理]
        PNL[盈亏追踪]
    end

    subgraph "交易执行"
        TE[交易引擎]
        GM[Gas管理]
        SP[滑点保护]
        TX[交易发送]
    end

    subgraph "监控分析"
        MON[实时监控]
        LOG[日志系统]
        ALT[告警系统]
        ANL[数据分析]
    end

    BC1 --> MS
    BC2 --> MS
    BC3 --> MS
    MS --> DS
    DS --> GW
    
    GW --> FT
    FT --> DM
    DM --> RL
    RL --> RC
    
    RC --> SE
    MC --> SE
    HP --> SE
    
    SE --> SM
    SE --> SI
    SE --> PT
    SE --> VS
    
    SM --> SA
    SI --> SA
    PT --> SA
    VS --> SA
    
    SA --> DEC
    DEC --> ML
    DEC --> RS
    RS --> PS
    
    PS --> OB
    OB --> PM
    PM --> TE
    
    TE --> GM
    GM --> SP
    SP --> TX
    
    TX --> MON
    TX --> PNL
    PNL --> ANL
    MON --> LOG
    MON --> ALT
    
    style GW fill:#f9f,stroke:#333,stroke-width:4px
    style SE fill:#bbf,stroke:#333,stroke-width:4px
    style DEC fill:#bfb,stroke:#333,stroke-width:4px
    style TE fill:#fbf,stroke:#333,stroke-width:4px
```

## 二、数据流转流程

```mermaid
flowchart LR
    subgraph "1. 数据采集"
        A1[链上交易] --> A2[Mempool监听]
        A2 --> A3[交易解析]
        A3 --> A4[数据标准化]
    end

    subgraph "2. 过滤处理"
        B1[貔貅检测]
        B2[黑名单过滤]
        B3[流动性检查]
        B4[异常检测]
        A4 --> B1
        B1 --> B2
        B2 --> B3
        B3 --> B4
    end

    subgraph "3. 策略评分"
        C1[并行策略计算]
        C2[权重分配]
        C3[综合评分]
        C4[阈值判断]
        B4 --> C1
        C1 --> C2
        C2 --> C3
        C3 --> C4
    end

    subgraph "4. 交易决策"
        D1{买入/卖出?}
        D2[仓位计算]
        D3[风险评估]
        D4[订单生成]
        C4 --> D1
        D1 -->|是| D2
        D2 --> D3
        D3 --> D4
    end

    subgraph "5. 执行反馈"
        E1[交易执行]
        E2[结果确认]
        E3[数据上报]
        E4[策略优化]
        D4 --> E1
        E1 --> E2
        E2 --> E3
        E3 --> E4
        E4 --> C1
    end
```

## 三、核心模块交互流程

```mermaid
graph LR
    subgraph "快速路径"
        FP1[热点Token检测]
        FP2[缓存命中]
        FP3[快速决策]
        FP4[立即执行]
        FP1 --> FP2
        FP2 --> FP3
        FP3 --> FP4
    end

    subgraph "标准路径"
        SP1[新Token发现]
        SP2[完整分析]
        SP3[策略评估]
        SP4[风险检查]
        SP5[交易执行]
        SP1 --> SP2
        SP2 --> SP3
        SP3 --> SP4
        SP4 --> SP5
    end

    subgraph "异常处理"
        EP1[异常检测]
        EP2[降级处理]
        EP3[人工介入]
        EP4[系统恢复]
        EP1 --> EP2
        EP2 --> EP3
        EP3 --> EP4
    end

    FP4 -.->|失败| EP1
    SP5 -.->|异常| EP1
    EP4 -.->|恢复| SP1
    EP4 -.->|恢复| FP1
```

## 四、多链并行处理流程

```mermaid
graph TB
    subgraph "链监听器集群"
        subgraph "EVM监听"
            E1[ETH监听器]
            E2[BSC监听器]
            E3[Polygon监听器]
        end
        
        subgraph "Solana监听"
            S1[Raydium监听]
            S2[Orca监听]
            S3[Meteora监听]
        end
        
        subgraph "Sui监听"
            SU1[Sui DEX监听]
        end
    end

    subgraph "消息队列"
        MQ1[EVM队列]
        MQ2[Solana队列]
        MQ3[Sui队列]
    end

    subgraph "处理器池"
        P1[处理器1]
        P2[处理器2]
        P3[处理器3]
        P4[处理器N]
    end

    E1 --> MQ1
    E2 --> MQ1
    E3 --> MQ1
    S1 --> MQ2
    S2 --> MQ2
    S3 --> MQ2
    SU1 --> MQ3

    MQ1 --> P1
    MQ1 --> P2
    MQ2 --> P2
    MQ2 --> P3
    MQ3 --> P3
    MQ3 --> P4

    P1 --> DB[统一数据处理]
    P2 --> DB
    P3 --> DB
    P4 --> DB
```

## 五、风险控制流程

```mermaid
flowchart TD
    START[交易信号] --> RC1{一级风控}
    
    RC1 -->|通过| RC2{二级风控}
    RC1 -->|拒绝| REJECT1[拒绝交易]
    
    RC2 -->|通过| RC3{三级风控}
    RC2 -->|警告| WARN[降低仓位]
    
    RC3 -->|通过| EXEC[执行交易]
    RC3 -->|拒绝| REJECT2[取消交易]
    
    WARN --> EXEC
    
    EXEC --> MON{实时监控}
    MON -->|正常| SUCCESS[交易完成]
    MON -->|异常| STOP[紧急止损]
    
    subgraph "一级风控"
        R1A[黑名单检查]
        R1B[流动性验证]
        R1C[合约安全检查]
    end
    
    subgraph "二级风控"
        R2A[仓位限制]
        R2B[杠杆控制]
        R2C[相关性检查]
    end
    
    subgraph "三级风控"
        R3A[滑点预测]
        R3B[Gas费用评估]
        R3C[MEV保护]
    end
    
    RC1 -.-> R1A
    RC1 -.-> R1B
    RC1 -.-> R1C
    
    RC2 -.-> R2A
    RC2 -.-> R2B
    RC2 -.-> R2C
    
    RC3 -.-> R3A
    RC3 -.-> R3B
    RC3 -.-> R3C
```

## 六、性能优化流程

```mermaid
graph LR
    subgraph "数据路径优化"
        DO1[原始数据]
        DO2[压缩传输]
        DO3[批量处理]
        DO4[并行计算]
        DO1 --> DO2
        DO2 --> DO3
        DO3 --> DO4
    end

    subgraph "缓存策略"
        CS1[L1缓存<br/>热点数据]
        CS2[L2缓存<br/>常用数据]
        CS3[L3存储<br/>历史数据]
        CS1 --> CS2
        CS2 --> CS3
    end

    subgraph "计算优化"
        CO1[预计算]
        CO2[增量计算]
        CO3[异步处理]
        CO4[结果缓存]
        CO1 --> CO2
        CO2 --> CO3
        CO3 --> CO4
    end

    DO4 --> CS1
    CS1 --> CO1
    CO4 --> RESULT[优化结果]

    style DO1 fill:#ffd,stroke:#333
    style CS1 fill:#dff,stroke:#333
    style CO1 fill:#fdf,stroke:#333
    style RESULT fill:#dfd,stroke:#333,stroke-width:3px
```

---
*流程图说明：*
1. **整体架构流程图**：展示系统各模块之间的关系和数据流向
2. **数据流转流程**：详细描述数据从采集到执行的完整路径
3. **核心模块交互**：区分快速路径、标准路径和异常处理
4. **多链并行处理**：展示如何同时处理多条链的数据
5. **风险控制流程**：多级风控体系的具体实施
6. **性能优化流程**：系统性能优化的关键路径

*注：可使用Mermaid渲染器查看上述流程图*