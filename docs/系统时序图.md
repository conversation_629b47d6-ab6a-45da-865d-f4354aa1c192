# MEV交易系统时序图

## 一、完整交易流程时序图

```mermaid
sequenceDiagram
    participant Chain as 区块链节点
    participant Gateway as 网关服务
    participant Cache as 缓存层
    participant Strategy as 策略引擎
    participant Decision as 决策模型
    participant Order as 订单管理
    participant Trade as 交易执行
    participant Monitor as 监控系统

    Chain->>Gateway: 1. 推送新交易/区块
    Gateway->>Gateway: 2. 貔貅检测
    Gateway->>Gateway: 3. 数据标准化
    Gateway->>Cache: 4. 缓存热点数据
    
    Gateway->>Strategy: 5. 发送交易事件
    
    par 并行策略计算
        Strategy->>Strategy: 6a. 聪明钱分析
    and
        Strategy->>Strategy: 6b. 社交影响分析
    and
        Strategy->>Strategy: 6c. 价格趋势分析
    and
        Strategy->>Strategy: 6d. 交易量分析
    end
    
    Strategy->>Strategy: 7. 策略评分聚合
    Strategy->>Decision: 8. 发送综合评分
    
    Decision->>Cache: 9. 查询历史数据
    Cache-->>Decision: 10. 返回历史数据
    
    Decision->>Decision: 11. 风险评估
    Decision->>Decision: 12. 仓位计算
    
    alt 评分达到买入阈值
        Decision->>Order: 13a. 生成买入订单
        Order->>Order: 14a. 检查现有仓位
        Order->>Trade: 15a. 发送买入指令
        Trade->>Trade: 16a. Gas优化
        Trade->>Trade: 17a. 滑点保护
        Trade->>Chain: 18a. 提交买入交易
        Chain-->>Trade: 19a. 交易确认
    else 评分达到卖出阈值
        Decision->>Order: 13b. 生成卖出订单
        Order->>Order: 14b. 检查持仓数量
        Order->>Trade: 15b. 发送卖出指令
        Trade->>Trade: 16b. Gas优化
        Trade->>Trade: 17b. 滑点保护
        Trade->>Chain: 18b. 提交卖出交易
        Chain-->>Trade: 19b. 交易确认
    else 评分在中性区间
        Decision->>Monitor: 13c. 记录但不交易
    end
    
    Trade->>Order: 20. 更新订单状态
    Trade->>Monitor: 21. 上报交易结果
    Monitor->>Monitor: 22. 计算盈亏
    Monitor->>Strategy: 23. 反馈优化策略
```

## 二、快速交易路径时序图

```mermaid
sequenceDiagram
    participant Mempool as Mempool监听
    participant FastPath as 快速通道
    participant HotCache as 热点缓存
    participant QuickDecision as 快速决策
    participant FlashTrade as 闪电交易
    participant Chain as 区块链

    Mempool->>FastPath: 1. 检测到高价值交易
    FastPath->>HotCache: 2. 查询Token缓存
    
    alt 缓存命中
        HotCache-->>FastPath: 3a. 返回预计算数据
        FastPath->>QuickDecision: 4a. 快速评估(< 1ms)
        QuickDecision->>QuickDecision: 5a. 简化风控
        QuickDecision->>FlashTrade: 6a. 立即执行
        
        par 抢先交易
            FlashTrade->>Chain: 7a. 高Gas优先交易
        and 监控确认
            FlashTrade->>Mempool: 7b. 监控交易状态
        end
        
        Chain-->>FlashTrade: 8. 交易确认
        FlashTrade-->>QuickDecision: 9. 结果反馈
    else 缓存未命中
        HotCache-->>FastPath: 3b. 无缓存数据
        FastPath->>FastPath: 4b. 降级到标准路径
        Note over FastPath: 转入标准交易流程
    end
```

## 三、多链协同交易时序图

```mermaid
sequenceDiagram
    participant ETH as ETH链
    participant SOL as Solana链
    participant SUI as Sui链
    participant Coordinator as 协调器
    participant Arbitrage as 套利引擎
    participant Execute as 执行器

    par 多链监听
        ETH->>Coordinator: 1a. ETH价格更新
    and
        SOL->>Coordinator: 1b. SOL价格更新
    and
        SUI->>Coordinator: 1c. SUI价格更新
    end

    Coordinator->>Coordinator: 2. 价差计算
    
    alt 发现套利机会
        Coordinator->>Arbitrage: 3. 套利信号
        Arbitrage->>Arbitrage: 4. 计算最优路径
        Arbitrage->>Arbitrage: 5. 评估手续费
        
        alt 有利可图
            Arbitrage->>Execute: 6. 生成交易序列
            
            Note over Execute: 原子性执行
            Execute->>ETH: 7a. 买入交易
            ETH-->>Execute: 8a. 确认
            Execute->>SOL: 7b. 卖出交易
            SOL-->>Execute: 8b. 确认
            
            Execute->>Coordinator: 9. 套利完成
            Coordinator->>Coordinator: 10. 记录收益
        else 无利可图
            Arbitrage->>Coordinator: 6. 放弃机会
        end
    end
```

## 四、风险控制时序图

```mermaid
sequenceDiagram
    participant Signal as 交易信号
    participant RiskL1 as 一级风控
    participant RiskL2 as 二级风控
    participant RiskL3 as 三级风控
    participant Execute as 执行器
    participant Emergency as 应急系统

    Signal->>RiskL1: 1. 提交交易请求
    
    RiskL1->>RiskL1: 2. 黑名单检查
    RiskL1->>RiskL1: 3. 流动性验证
    
    alt 一级通过
        RiskL1->>RiskL2: 4a. 进入二级
        RiskL2->>RiskL2: 5a. 仓位检查
        RiskL2->>RiskL2: 6a. 相关性分析
        
        alt 二级通过
            RiskL2->>RiskL3: 7a. 进入三级
            RiskL3->>RiskL3: 8a. 滑点预测
            RiskL3->>RiskL3: 9a. MEV保护检查
            
            alt 三级通过
                RiskL3->>Execute: 10a. 批准执行
                Execute->>Execute: 11a. 执行交易
                Execute-->>Signal: 12a. 成功
            else 三级拒绝
                RiskL3->>Signal: 10b. 高风险拒绝
                RiskL3->>Emergency: 11b. 记录风险
            end
        else 二级警告
            RiskL2->>RiskL2: 7b. 降低仓位
            RiskL2->>RiskL3: 8b. 受限执行
        end
    else 一级拒绝
        RiskL1->>Signal: 4b. 直接拒绝
        RiskL1->>Emergency: 5b. 触发告警
    end
```

## 五、策略优化反馈时序图

```mermaid
sequenceDiagram
    participant Trade as 交易执行
    participant Result as 结果收集
    participant Analyze as 分析引擎
    participant ML as 机器学习
    participant Strategy as 策略更新
    participant Config as 配置中心

    Trade->>Result: 1. 交易完成
    Result->>Result: 2. 记录交易详情
    Result->>Result: 3. 计算实际盈亏
    
    Result->>Analyze: 4. 发送结果数据
    
    par 数据分析
        Analyze->>Analyze: 5a. 成功率分析
    and
        Analyze->>Analyze: 5b. 收益率分析
    and
        Analyze->>Analyze: 5c. 风险指标分析
    end
    
    Analyze->>ML: 6. 输入训练数据
    
    ML->>ML: 7. 模型训练
    ML->>ML: 8. 参数优化
    ML->>ML: 9. 回测验证
    
    alt 优化效果显著
        ML->>Strategy: 10a. 推送新策略
        Strategy->>Config: 11a. 更新配置
        Config->>Config: 12a. 热更新
        Config-->>Strategy: 13a. 配置生效
        Strategy-->>ML: 14a. 确认更新
    else 优化效果不佳
        ML->>ML: 10b. 继续训练
        ML->>Analyze: 11b. 请求更多数据
    end
```

## 六、异常处理时序图

```mermaid
sequenceDiagram
    participant System as 系统
    participant Detector as 异常检测
    participant Handler as 处理器
    participant Fallback as 降级服务
    participant Alert as 告警系统
    participant Recovery as 恢复系统

    System->>Detector: 1. 系统运行
    
    alt 检测到异常
        Detector->>Detector: 2. 异常分类
        
        alt 轻微异常
            Detector->>Handler: 3a. 自动处理
            Handler->>Handler: 4a. 重试机制
            Handler->>System: 5a. 恢复正常
        else 中度异常
            Detector->>Fallback: 3b. 服务降级
            Fallback->>Fallback: 4b. 启用备用方案
            Fallback->>Alert: 5b. 发送警告
            Alert->>Alert: 6b. 通知运维
        else 严重异常
            Detector->>Handler: 3c. 紧急停止
            Handler->>System: 4c. 停止交易
            Handler->>Alert: 5c. 紧急告警
            Alert->>Alert: 6c. 多渠道通知
            
            Note over Recovery: 人工介入
            Recovery->>System: 7c. 系统诊断
            Recovery->>System: 8c. 修复问题
            Recovery->>System: 9c. 逐步恢复
        end
    end
    
    System->>Detector: 10. 继续监控
```

## 七、实时监控时序图

```mermaid
sequenceDiagram
    participant Events as 事件流
    participant Collector as 采集器
    participant Metrics as 指标计算
    participant Dashboard as 监控面板
    participant Alerting as 告警引擎
    participant Storage as 存储

    loop 实时监控循环
        Events->>Collector: 1. 事件推送
        Collector->>Collector: 2. 数据清洗
        Collector->>Metrics: 3. 发送数据
        
        par 指标计算
            Metrics->>Metrics: 4a. 延迟指标
        and
            Metrics->>Metrics: 4b. 吞吐量指标
        and
            Metrics->>Metrics: 4c. 错误率指标
        and
            Metrics->>Metrics: 4d. 业务指标
        end
        
        Metrics->>Dashboard: 5. 更新面板
        Metrics->>Storage: 6. 持久化存储
        
        Metrics->>Alerting: 7. 检查阈值
        
        alt 触发告警
            Alerting->>Alerting: 8a. 告警分级
            alt Critical级别
                Alerting->>Alerting: 9a. 立即通知
                Note over Alerting: SMS + 电话
            else Warning级别
                Alerting->>Alerting: 9b. 消息通知
                Note over Alerting: Telegram + Email
            else Info级别
                Alerting->>Storage: 9c. 仅记录
            end
        end
    end
```

---

*时序图说明：*

1. **完整交易流程**：展示从链上数据到交易执行的完整时序
2. **快速交易路径**：针对高价值机会的快速响应机制
3. **多链协同交易**：跨链套利的协调执行流程
4. **风险控制流程**：三级风控的详细执行时序
5. **策略优化反馈**：基于交易结果的策略自动优化
6. **异常处理流程**：不同级别异常的处理机制
7. **实时监控流程**：系统监控和告警的时序关系

*这些时序图清晰展示了系统各组件的交互顺序和并行处理能力，有助于理解系统的动态行为。*