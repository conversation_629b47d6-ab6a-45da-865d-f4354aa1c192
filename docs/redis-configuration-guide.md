# Redis 配置使用指南

## 概述

本项目的 Redis 配置系统支持多种配置方式，可以灵活适应不同的部署环境和需求。

## 配置方式

### 1. 统一配置（推荐）

在项目根目录创建 `.env` 文件，所有服务共享配置：

```bash
# 根目录 .env
NODE_ENV=development

# Redis 配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 开发环境 Redis 配置
DEV_REDIS_HOST=127.0.0.1
DEV_REDIS_PORT=6379
DEV_REDIS_PASSWORD=
DEV_REDIS_DB=0
```

### 2. 服务独立配置

如果某个服务需要特殊的 Redis 配置，可以在服务目录创建 `.env` 文件：

```bash
# services/gateway/.env
GATEWAY_REDIS_HOST=gateway-redis.example.com
GATEWAY_REDIS_PORT=6379
GATEWAY_REDIS_PASSWORD=gateway-secret
GATEWAY_REDIS_DB=1
```

### 3. 代码中直接配置

```typescript
import { RedisServiceConfig } from '@mev-bot/shared';

const customConfig: RedisServiceConfig = {
    redis: {
        host: 'localhost',
        port: 6379,
        password: 'mypassword',
        db: 0
    },
    isDev: true,
    enableLogging: true
};
```

## 使用方式

### 在服务中使用

```typescript
import { 
    createRedisConfigFromEnv,
    getSingleRedisPubSubService,
    autoLoadEnv 
} from '@mev-bot/shared';

export class MyService {
    private redisPubSubService: RedisPubSubService;

    constructor(config?: RedisServiceConfig) {
        // 方式 1: 使用传入的配置
        if (config) {
            this.redisPubSubService = getSingleRedisPubSubService(config);
            return;
        }

        // 方式 2: 自动加载 .env 文件
        autoLoadEnv(__dirname);
        const envConfig = createRedisConfigFromEnv(true);
        this.redisPubSubService = getSingleRedisPubSubService(envConfig);

        // 方式 3: 手动指定 .env 文件路径
        // const envPath = path.join(__dirname, '.env');
        // const envConfig = createRedisConfigFromEnv(true, envPath);
        // this.redisPubSubService = getSingleRedisPubSubService(envConfig);
    }
}
```

### 在主应用中使用

```typescript
import { getAppRedisConfig } from './redis-config';

async function main() {
    // 获取统一的 Redis 配置
    const redisConfig = getAppRedisConfig();
    
    // 传递给各个服务
    const gatewayService = await SolanaGatewayService.init(redisConfig);
    const decisionService = new SolanaDecisionService(redisConfig);
    const chainsService = new ChainsService(redisConfig);
}
```

## 环境变量说明

### 通用环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `REDIS_HOST` | Redis 主机地址 | `127.0.0.1` |
| `REDIS_PORT` | Redis 端口 | `6379` |
| `REDIS_PASSWORD` | Redis 密码 | 无 |
| `REDIS_DB` | Redis 数据库编号 | `0` |
| `REDIS_ENABLE_LOGGING` | 是否启用日志 | `false` |

### 开发环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `DEV_REDIS_HOST` | 开发环境 Redis 主机 | `127.0.0.1` |
| `DEV_REDIS_PORT` | 开发环境 Redis 端口 | `6379` |
| `DEV_REDIS_PASSWORD` | 开发环境 Redis 密码 | 无 |
| `DEV_REDIS_DB` | 开发环境 Redis 数据库 | `0` |

### 服务专用变量（可选）

每个服务可以定义自己的专用变量，例如：

```bash
# Gateway 服务专用
GATEWAY_REDIS_HOST=gateway-redis.example.com
GATEWAY_REDIS_DB=1

# Chains 服务专用
CHAINS_REDIS_HOST=chains-redis.example.com
CHAINS_REDIS_DB=2
```

## 最佳实践

### 1. 开发环境

- 在项目根目录创建 `.env` 文件
- 使用 `autoLoadEnv()` 自动加载配置
- 启用日志记录便于调试

### 2. 生产环境

- 使用系统环境变量或容器环境变量
- 不要在代码中硬编码敏感信息
- 关闭日志记录提高性能

### 3. 测试环境

- 使用独立的 Redis 数据库（如 db=15）
- 关闭日志记录避免干扰测试输出
- 使用 `createTestRedisConfig()` 快速创建测试配置

## 故障排除

### 1. 找不到 .env 文件

```typescript
// 手动指定 .env 文件路径
const config = createRedisConfigFromEnv(true, '/path/to/.env');
```

### 2. Redis 连接失败

检查环境变量是否正确设置：

```typescript
console.log('Redis Config:', {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    password: process.env.REDIS_PASSWORD ? '***' : 'none'
});
```

### 3. 配置不生效

确保在使用配置之前调用了 `autoLoadEnv()` 或手动加载了 `.env` 文件。
