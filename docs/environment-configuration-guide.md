# 环境配置使用指南

## 概述

本项目使用单一的 `.env` 文件进行环境配置管理，简单实用。部署到不同环境时，只需要修改 `.env` 文件中的配置即可。

## 配置文件

项目只需要一个 `.env` 文件，包含所有必要的配置：

```bash
# 环境配置
NODE_ENV=development

# Redis 配置
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_ENABLE_LOGGING=true

# Solana 配置
ENDPOINT=
GRPC_ENDPOINT=

# 应用配置
DEBUG_MODE=true
LOG_LEVEL=debug
```

## 不同环境的配置示例

### 开发环境配置
```bash
NODE_ENV=development
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_ENABLE_LOGGING=true
DEBUG_MODE=true
```

### 生产环境配置
```bash
NODE_ENV=production
REDIS_HOST=prod-redis.example.com
REDIS_PORT=6379
REDIS_PASSWORD=your_production_password
REDIS_ENABLE_LOGGING=false
DEBUG_MODE=false
```

### 测试环境配置
```bash
NODE_ENV=test
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=15
REDIS_ENABLE_LOGGING=false
DEBUG_MODE=false
```

## 使用方式

### 直接运行服务

```bash
# 开发环境
npm run dev:orders

# 生产环境
npm run start:orders
```

### 在代码中使用

```typescript
import { autoLoadEnv, createRedisConfigFromEnv } from '@mev-bot/shared';

// 自动加载 .env 文件
autoLoadEnv(__dirname);

// 创建配置（根据 NODE_ENV 自动判断是否为开发环境）
const config = createRedisConfigFromEnv(process.env.NODE_ENV === 'development');
```

## 在服务中的使用

```typescript
import { autoLoadEnv, createRedisConfigFromEnv } from '@mev-bot/shared';

export class MyService {
    constructor() {
        // 自动加载 .env 文件
        autoLoadEnv(__dirname);

        // 创建 Redis 配置
        const config = createRedisConfigFromEnv(
            process.env.NODE_ENV === 'development'
        );
    }
}
```

## 环境变量说明

### 通用环境变量

| 变量名 | 说明 | 开发环境默认值 | 生产环境默认值 |
|--------|------|----------------|----------------|
| `NODE_ENV` | 运行环境 | `development` | `production` |
| `REDIS_HOST` | Redis 主机 | `127.0.0.1` | `prod-redis.example.com` |
| `REDIS_PORT` | Redis 端口 | `6379` | `6379` |
| `REDIS_PASSWORD` | Redis 密码 | 无 | 生产密码 |
| `REDIS_DB` | Redis 数据库 | `0` | `0` |
| `REDIS_ENABLE_LOGGING` | 启用 Redis 日志 | `true` | `false` |

### 应用特定环境变量

| 变量名 | 说明 | 开发环境 | 生产环境 |
|--------|------|----------|----------|
| `DEBUG_MODE` | 调试模式 | `true` | `false` |
| `LOG_LEVEL` | 日志级别 | `debug` | `info` |
| `MOCK_EXTERNAL_APIS` | 模拟外部 API | `true` | `false` |

## 部署管理

### 开发环境
直接使用项目中的 `.env` 文件，配置本地开发所需的参数。

### 生产环境
部署时修改 `.env` 文件中的配置：
```bash
# 修改为生产环境配置
NODE_ENV=production
REDIS_HOST=your-production-redis-host
REDIS_PASSWORD=your-production-password
```

### 最佳实践

1. **版本控制**：
   - 提交 `.env.example` 作为配置模板
   - 不要提交实际的 `.env` 文件（添加到 .gitignore）

2. **.gitignore 配置**：
```bash
# 环境配置文件
.env
```

### Docker 部署

```dockerfile
# Dockerfile
FROM node:18
WORKDIR /app

# 复制并修改配置文件
COPY .env.example .env
# 在构建时或运行时修改 .env 文件中的配置

# 或者通过环境变量传入
ENV NODE_ENV=production
ENV REDIS_HOST=redis-server
ENV REDIS_PASSWORD=secret

COPY . .
RUN npm install && npm run build
CMD ["npm", "start"]
```

### 容器编排

```yaml
# docker-compose.yml
version: '3.8'
services:
  orders-service:
    build: ./services/orders
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
      - REDIS_PASSWORD=secret123
    depends_on:
      - redis

  redis:
    image: redis:alpine
    command: redis-server --requirepass secret123
```

## 调试和验证

### 查看加载的配置文件

启动服务时会显示加载的配置文件：

```bash
npm run dev:orders
# 输出：Loaded .env files: /path/to/.env.development, /path/to/.env
```

### 验证环境变量

```typescript
console.log('Environment:', process.env.NODE_ENV);
console.log('Redis Host:', process.env.REDIS_HOST);
console.log('Debug Mode:', process.env.DEBUG_MODE);
```

## 故障排除

### 1. 配置文件未加载

- 检查文件路径是否正确
- 确认文件名拼写正确
- 查看控制台输出的加载信息

### 2. 环境变量未生效

- 确认 NODE_ENV 设置正确
- 检查配置文件优先级
- 验证环境变量语法

### 3. 本地开发配置

创建 `.env.development.local` 文件进行本地调试：

```bash
# .env.development.local
REDIS_HOST=localhost
REDIS_PORT=6380
DEBUG_MODE=true
```

这样您就可以灵活地管理不同环境的配置了！
