# 简化版MEV交易系统架构设计（纯TypeScript）

## 一、设计理念

基于您的策略思路，系统核心是**事件驱动的评分机制**，不涉及复杂计算，TypeScript完全够用。重点在于：
- **快速响应**：事件驱动，实时评分
- **简单高效**：评分逻辑简单，易于调整
- **易于扩展**：新策略即插即用

## 二、技术选型（简化版）

```yaml
核心技术栈:
  语言: TypeScript (100%)
  运行时: Node.js 20+ / Bun (更快)
  框架: NestJS (模块化) 或 Express (轻量)
  
数据层:
  缓存: Redis (热点数据)
  数据库: PostgreSQL (交易记录)
  消息队列: Redis Pub/Sub (简单够用)
  
链接层:
  EVM: ethers.js v6
  Solana: @solana/web3.js
  WebSocket: ws (原生WebSocket)
  
监控:
  日志: winston
  监控: PM2 + 自定义仪表盘
```

## 三、核心模块实现

### 3.1 网关层（Gateway）

```typescript
// gateway-service.ts
import { EventEmitter } from 'events';
import { WebSocketProvider } from 'ethers';
import { SwapModel } from './model/model-types';

export class GatewayService extends EventEmitter {
    private providers: Map<string, WebSocketProvider> = new Map();
    private redis: Redis;
    
    constructor() {
        super();
        this.initProviders();
        this.redis = new Redis();
    }
    
    private initProviders() {
        // 多个节点提高可靠性
        const endpoints = [
            'wss://mainnet.infura.io/ws/v3/YOUR_KEY',
            'wss://eth-mainnet.g.alchemy.com/v2/YOUR_KEY',
        ];
        
        endpoints.forEach((url, index) => {
            const provider = new WebSocketProvider(url);
            this.providers.set(`provider_${index}`, provider);
            this.setupListeners(provider);
        });
    }
    
    private setupListeners(provider: WebSocketProvider) {
        // 监听pending交易
        provider.on('pending', async (txHash) => {
            try {
                const tx = await provider.getTransaction(txHash);
                if (!tx) return;
                
                // 快速过滤：只关注DEX交易
                if (!this.isDexTransaction(tx)) return;
                
                // 转换为统一模型
                const swapModel = await this.parseTransaction(tx);
                
                // 基础过滤
                if (await this.basicFilter(swapModel)) {
                    // 发送到策略层
                    this.emit('swap', swapModel);
                    
                    // 缓存热点数据
                    await this.cacheHotData(swapModel);
                }
            } catch (error) {
                console.error('处理交易失败:', error);
            }
        });
    }
    
    private isDexTransaction(tx: any): boolean {
        // 检查是否是DEX交易（简化版）
        const DEX_ADDRESSES = [
            '******************************************', // Uniswap V2 Router
            '0xE592427A0AEce92De3Edee1F18E0157C05861564', // Uniswap V3 Router
            // ... 其他DEX地址
        ];
        
        return DEX_ADDRESSES.includes(tx.to?.toLowerCase());
    }
    
    private async parseTransaction(tx: any): Promise<SwapModel> {
        // 解析交易数据
        const decoded = this.decodeSwapData(tx.data);
        
        return {
            txHash: tx.hash,
            from: tx.from,
            to: tx.to,
            tokenIn: decoded.tokenIn,
            tokenOut: decoded.tokenOut,
            amountIn: decoded.amountIn,
            amountOut: decoded.amountOut,
            gasPrice: tx.gasPrice,
            timestamp: Date.now(),
            dex: this.identifyDex(tx.to),
        };
    }
    
    private async basicFilter(swap: SwapModel): Promise<boolean> {
        // 1. 检查是否是貔貅
        if (await this.isHoneypot(swap.tokenIn) || await this.isHoneypot(swap.tokenOut)) {
            return false;
        }
        
        // 2. 检查流动性
        const liquidity = await this.getLiquidity(swap.tokenIn, swap.tokenOut);
        if (liquidity < 10000) { // 最小流动性$10k
            return false;
        }
        
        // 3. 检查交易量
        if (Number(swap.amountIn) < 100) { // 最小交易量$100
            return false;
        }
        
        return true;
    }
    
    private async isHoneypot(token: string): Promise<boolean> {
        // 从缓存检查
        const cached = await this.redis.get(`honeypot:${token}`);
        if (cached !== null) return cached === 'true';
        
        // 简单的貔貅检测逻辑
        try {
            // 检查是否能卖出
            const canSell = await this.checkCanSell(token);
            
            // 缓存结果（1小时）
            await this.redis.setex(`honeypot:${token}`, 3600, canSell ? 'false' : 'true');
            
            return !canSell;
        } catch {
            return true; // 出错就当作貔貅
        }
    }
}
```

### 3.2 策略引擎（Strategy Engine）

```typescript
// strategy-processor.ts
import { EventEmitter } from 'events';
import { SwapModel } from '../gateway/model/model-types';
import { BaseSimpleStrategy } from './simple/base-simple-strategy';

export class StrategyProcessor extends EventEmitter {
    private strategies: Map<string, BaseSimpleStrategy> = new Map();
    private scoreCache: Map<string, ScoreCache> = new Map();
    
    // 阈值配置
    private readonly BUY_THRESHOLD = 70;   // 买入阈值
    private readonly SELL_THRESHOLD = 30;  // 卖出阈值
    
    constructor() {
        super();
        this.initStrategies();
    }
    
    private initStrategies() {
        // 注册所有策略
        this.strategies.set('smart_money', new SmartMoneyStrategy(0.3));
        this.strategies.set('social', new SocialStrategy(0.2));
        this.strategies.set('price_trend', new PriceTrendStrategy(0.25));
        this.strategies.set('volume', new VolumeStrategy(0.25));
    }
    
    async processSwap(swap: SwapModel): Promise<void> {
        try {
            // 1. 并行执行所有策略
            const scores = await this.calculateScores(swap);
            
            // 2. 加权计算总分
            const totalScore = this.calculateWeightedScore(scores);
            
            // 3. 更新缓存
            this.updateScoreCache(swap.tokenOut, totalScore);
            
            // 4. 检查是否触发交易信号
            const signal = this.checkSignal(swap.tokenOut, totalScore);
            
            if (signal) {
                // 5. 发送交易信号
                this.emit('signal', {
                    token: swap.tokenOut,
                    action: signal.action,
                    score: totalScore,
                    confidence: this.calculateConfidence(scores),
                    amount: this.calculateAmount(totalScore, signal.action),
                    swap: swap,
                    timestamp: Date.now()
                });
            }
            
            // 6. 记录评分历史（用于回测）
            await this.recordScore(swap, totalScore, scores);
            
        } catch (error) {
            console.error('策略处理失败:', error);
        }
    }
    
    private async calculateScores(swap: SwapModel): Promise<Map<string, number>> {
        const scores = new Map<string, number>();
        
        // 并行计算所有策略分数
        const promises = Array.from(this.strategies.entries()).map(async ([name, strategy]) => {
            const score = await strategy.analyze(swap);
            return { name, score: score.score };
        });
        
        const results = await Promise.all(promises);
        results.forEach(r => scores.set(r.name, r.score));
        
        return scores;
    }
    
    private calculateWeightedScore(scores: Map<string, number>): number {
        let totalScore = 0;
        let totalWeight = 0;
        
        scores.forEach((score, name) => {
            const strategy = this.strategies.get(name);
            if (strategy) {
                totalScore += score * strategy.weight;
                totalWeight += strategy.weight;
            }
        });
        
        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }
    
    private checkSignal(token: string, score: number): Signal | null {
        const cache = this.scoreCache.get(token);
        
        // 买入信号
        if (score >= this.BUY_THRESHOLD) {
            // 检查是否已持仓
            if (!this.hasPosition(token)) {
                return { action: 'BUY', strength: (score - this.BUY_THRESHOLD) / (100 - this.BUY_THRESHOLD) };
            }
        }
        
        // 卖出信号
        if (score <= this.SELL_THRESHOLD) {
            // 检查是否有持仓
            if (this.hasPosition(token)) {
                return { action: 'SELL', strength: (this.SELL_THRESHOLD - score) / this.SELL_THRESHOLD };
            }
        }
        
        return null;
    }
    
    private calculateAmount(score: number, action: string): number {
        // 根据分数计算交易量
        const baseAmount = 1000; // 基础金额 $1000
        
        if (action === 'BUY') {
            // 分数越高，买入越多
            const multiplier = (score - this.BUY_THRESHOLD) / (100 - this.BUY_THRESHOLD);
            return baseAmount * (1 + multiplier * 2); // 最多3倍基础金额
        } else {
            // 分数越低，卖出越多
            const multiplier = (this.SELL_THRESHOLD - score) / this.SELL_THRESHOLD;
            return baseAmount * (1 + multiplier * 2);
        }
    }
}

// 具体策略实现示例
class SmartMoneyStrategy implements BaseSimpleStrategy {
    constructor(public readonly weight: number) {}
    
    async analyze(swap: SwapModel): Promise<StrategyScoreResult> {
        let score = 50; // 基础分50
        
        // 1. 检查是否是聪明钱地址
        if (await this.isSmartMoney(swap.from)) {
            score += 20;
        }
        
        // 2. 检查交易金额
        const amount = Number(swap.amountIn);
        if (amount > 10000) score += 10;  // 大额交易
        if (amount > 50000) score += 10;  // 超大额交易
        
        // 3. 检查Gas费用（愿意付高Gas说明看好）
        const gasPrice = Number(swap.gasPrice);
        if (gasPrice > 100) score += 5;
        if (gasPrice > 200) score += 5;
        
        return {
            score: Math.min(100, Math.max(0, score)),
            strategy: 'smart_money',
            details: {
                isSmartMoney: await this.isSmartMoney(swap.from),
                amount: amount,
                gasPrice: gasPrice
            }
        };
    }
    
    private async isSmartMoney(address: string): Promise<boolean> {
        // 聪明钱地址列表（可以从数据库或API获取）
        const SMART_MONEY_ADDRESSES = [
            // 知名交易者地址
            '0x...',
            // 成功的MEV机器人地址
            '0x...',
        ];
        
        return SMART_MONEY_ADDRESSES.includes(address.toLowerCase());
    }
}

class VolumeStrategy implements BaseSimpleStrategy {
    constructor(public readonly weight: number) {}
    
    async analyze(swap: SwapModel): Promise<StrategyScoreResult> {
        let score = 50;
        
        // 获取最近的交易量数据
        const recentVolume = await this.getRecentVolume(swap.tokenOut);
        const avgVolume = await this.getAverageVolume(swap.tokenOut);
        
        // 交易量激增是好信号
        const volumeRatio = recentVolume / avgVolume;
        if (volumeRatio > 2) score += 20;
        if (volumeRatio > 5) score += 20;
        
        // 检查买卖比
        const buyRatio = await this.getBuyRatio(swap.tokenOut);
        if (buyRatio > 0.6) score += 10;
        if (buyRatio > 0.8) score += 10;
        
        return {
            score: Math.min(100, Math.max(0, score)),
            strategy: 'volume',
            details: {
                recentVolume,
                avgVolume,
                volumeRatio,
                buyRatio
            }
        };
    }
    
    private async getRecentVolume(token: string): Promise<number> {
        // 从Redis获取最近1小时的交易量
        const key = `volume:${token}:${Math.floor(Date.now() / 3600000)}`;
        return Number(await redis.get(key)) || 0;
    }
    
    private async getAverageVolume(token: string): Promise<number> {
        // 获取24小时平均交易量
        // 实际实现...
        return 10000;
    }
    
    private async getBuyRatio(token: string): Promise<number> {
        // 获取买入比例
        // 实际实现...
        return 0.5;
    }
}
```

### 3.3 决策模块（Decision）

```typescript
// decision-service.ts
export class DecisionService {
    private positions: Map<string, Position> = new Map();
    private readonly MAX_POSITIONS = 10;
    private readonly MAX_POSITION_SIZE = 10000; // 单个仓位最大$10k
    private readonly TOTAL_CAPITAL = 100000; // 总资金$100k
    
    async processSignal(signal: TradingSignal): Promise<Decision> {
        // 1. 风险检查
        const riskCheck = await this.checkRisk(signal);
        if (!riskCheck.passed) {
            return { action: 'REJECT', reason: riskCheck.reason };
        }
        
        // 2. 仓位管理
        const positionSize = this.calculatePositionSize(signal);
        
        // 3. 检查资金
        if (!this.hasEnoughCapital(positionSize)) {
            return { action: 'REJECT', reason: 'Insufficient capital' };
        }
        
        // 4. 生成决策
        return {
            action: signal.action,
            token: signal.token,
            amount: positionSize,
            maxSlippage: 0.02, // 2%滑点
            deadline: Date.now() + 60000, // 1分钟超时
            gasPrice: await this.getOptimalGasPrice(),
        };
    }
    
    private async checkRisk(signal: TradingSignal): Promise<RiskCheck> {
        // 1. 检查仓位数量
        if (this.positions.size >= this.MAX_POSITIONS && signal.action === 'BUY') {
            return { passed: false, reason: 'Max positions reached' };
        }
        
        // 2. 检查单个仓位大小
        if (signal.amount > this.MAX_POSITION_SIZE) {
            return { passed: false, reason: 'Position too large' };
        }
        
        // 3. 检查总暴露
        const totalExposure = this.getTotalExposure();
        if (totalExposure + signal.amount > this.TOTAL_CAPITAL * 0.8) {
            return { passed: false, reason: 'Total exposure too high' };
        }
        
        // 4. 检查token风险
        const tokenRisk = await this.assessTokenRisk(signal.token);
        if (tokenRisk > 0.7) {
            return { passed: false, reason: 'Token risk too high' };
        }
        
        return { passed: true };
    }
    
    private calculatePositionSize(signal: TradingSignal): number {
        // Kelly公式简化版
        const confidence = signal.confidence || 0.6;
        const winRate = 0.55; // 假设55%胜率
        const avgWinLoss = 1.5; // 平均盈亏比
        
        // Kelly = (p * b - q) / b
        // p: 胜率, q: 败率, b: 盈亏比
        const kelly = (winRate * avgWinLoss - (1 - winRate)) / avgWinLoss;
        
        // 使用1/4 Kelly降低风险
        const positionRatio = Math.max(0, Math.min(kelly * 0.25, 0.1));
        
        // 根据信号强度调整
        const baseAmount = this.TOTAL_CAPITAL * positionRatio;
        return baseAmount * signal.score / 100;
    }
    
    private getTotalExposure(): number {
        let total = 0;
        this.positions.forEach(p => total += p.value);
        return total;
    }
}
```

### 3.4 交易执行（Trade Execution）

```typescript
// trade-service.ts
import { ethers } from 'ethers';

export class TradeService {
    private wallet: ethers.Wallet;
    private routers: Map<string, ethers.Contract> = new Map();
    
    constructor() {
        this.initWallet();
        this.initRouters();
    }
    
    private initWallet() {
        const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
        this.wallet = new ethers.Wallet(process.env.PRIVATE_KEY!, provider);
    }
    
    private initRouters() {
        // Uniswap V2 Router
        this.routers.set('uniswap_v2', new ethers.Contract(
            '******************************************',
            UNISWAP_V2_ABI,
            this.wallet
        ));
        
        // 其他DEX...
    }
    
    async executeTrade(decision: Decision): Promise<TradeResult> {
        try {
            // 1. 选择最佳路由
            const route = await this.findBestRoute(
                decision.token,
                decision.amount
            );
            
            // 2. 构建交易
            const tx = await this.buildTransaction(route, decision);
            
            // 3. 模拟交易
            const simulation = await this.simulateTransaction(tx);
            if (!simulation.success) {
                throw new Error(`Simulation failed: ${simulation.reason}`);
            }
            
            // 4. 发送交易
            const receipt = await this.sendTransaction(tx);
            
            // 5. 等待确认
            await receipt.wait(1);
            
            // 6. 记录结果
            return {
                success: true,
                txHash: receipt.hash,
                gasUsed: receipt.gasUsed,
                effectivePrice: this.calculateEffectivePrice(receipt),
            };
            
        } catch (error) {
            console.error('交易执行失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    private async findBestRoute(token: string, amount: number): Promise<Route> {
        // 简单实现：比较几个主要DEX的价格
        const quotes = await Promise.all([
            this.getUniswapQuote(token, amount),
            this.getSushiswapQuote(token, amount),
            // ... 其他DEX
        ]);
        
        // 选择最优价格
        return quotes.reduce((best, current) => 
            current.outputAmount > best.outputAmount ? current : best
        );
    }
    
    private async buildTransaction(route: Route, decision: Decision): Promise<any> {
        const router = this.routers.get(route.dex);
        if (!router) throw new Error(`Router not found: ${route.dex}`);
        
        const deadline = Math.floor(Date.now() / 1000) + 60; // 1分钟超时
        
        if (decision.action === 'BUY') {
            // ETH -> Token
            return router.swapExactETHForTokens(
                route.minAmountOut,
                route.path,
                this.wallet.address,
                deadline,
                {
                    value: ethers.parseEther(decision.amount.toString()),
                    gasPrice: decision.gasPrice,
                    gasLimit: 300000
                }
            );
        } else {
            // Token -> ETH
            return router.swapExactTokensForETH(
                ethers.parseUnits(decision.amount.toString(), route.decimals),
                route.minAmountOut,
                route.path,
                this.wallet.address,
                deadline,
                {
                    gasPrice: decision.gasPrice,
                    gasLimit: 300000
                }
            );
        }
    }
    
    private async simulateTransaction(tx: any): Promise<SimulationResult> {
        try {
            // 使用eth_call模拟
            const result = await this.wallet.provider.call(tx);
            return { success: true, result };
        } catch (error) {
            return { success: false, reason: error.message };
        }
    }
}
```

### 3.5 订单管理（Order Management）

```typescript
// order-service.ts
export class OrderService {
    private positions: Map<string, Position> = new Map();
    private orders: Map<string, Order> = new Map();
    private pnl: PnLTracker;
    
    constructor() {
        this.pnl = new PnLTracker();
    }
    
    async createOrder(decision: Decision): Promise<Order> {
        const order: Order = {
            id: this.generateOrderId(),
            token: decision.token,
            action: decision.action,
            amount: decision.amount,
            status: 'PENDING',
            createdAt: Date.now(),
            maxSlippage: decision.maxSlippage,
            deadline: decision.deadline,
        };
        
        this.orders.set(order.id, order);
        return order;
    }
    
    async updatePosition(order: Order, result: TradeResult): Promise<void> {
        if (!result.success) {
            order.status = 'FAILED';
            return;
        }
        
        order.status = 'COMPLETED';
        order.executedPrice = result.effectivePrice;
        order.txHash = result.txHash;
        
        // 更新仓位
        if (order.action === 'BUY') {
            this.addPosition(order);
        } else {
            this.reducePosition(order);
        }
        
        // 更新PnL
        await this.pnl.update(order);
    }
    
    private addPosition(order: Order): void {
        const existing = this.positions.get(order.token);
        
        if (existing) {
            // 加仓：计算新的平均价格
            const totalValue = existing.amount * existing.avgPrice + order.amount * order.executedPrice;
            const totalAmount = existing.amount + order.amount;
            
            existing.amount = totalAmount;
            existing.avgPrice = totalValue / totalAmount;
            existing.lastUpdate = Date.now();
        } else {
            // 新仓位
            this.positions.set(order.token, {
                token: order.token,
                amount: order.amount,
                avgPrice: order.executedPrice,
                openTime: Date.now(),
                lastUpdate: Date.now(),
                unrealizedPnL: 0,
            });
        }
    }
    
    private reducePosition(order: Order): void {
        const position = this.positions.get(order.token);
        if (!position) return;
        
        // 计算已实现盈亏
        const realizedPnL = (order.executedPrice - position.avgPrice) * order.amount;
        
        // 更新仓位
        position.amount -= order.amount;
        
        if (position.amount <= 0) {
            // 清仓
            this.positions.delete(order.token);
        }
        
        // 记录盈亏
        this.pnl.addRealized(realizedPnL);
    }
    
    async getPositionStats(): Promise<PositionStats> {
        const positions = Array.from(this.positions.values());
        const totalValue = positions.reduce((sum, p) => sum + p.amount * p.avgPrice, 0);
        
        // 获取当前价格计算未实现盈亏
        for (const position of positions) {
            const currentPrice = await this.getCurrentPrice(position.token);
            position.unrealizedPnL = (currentPrice - position.avgPrice) * position.amount;
        }
        
        return {
            count: positions.length,
            totalValue,
            positions,
            realizedPnL: this.pnl.getRealized(),
            unrealizedPnL: positions.reduce((sum, p) => sum + p.unrealizedPnL, 0),
        };
    }
}
```

## 四、关键配置和优化

### 4.1 Redis缓存策略

```typescript
// cache-config.ts
export class CacheManager {
    private redis: Redis;
    
    constructor() {
        this.redis = new Redis({
            host: 'localhost',
            port: 6379,
            db: 0,
            keyPrefix: 'mev:',
        });
    }
    
    // 缓存策略
    async cacheWithTTL(key: string, value: any, ttl: number): Promise<void> {
        await this.redis.setex(key, ttl, JSON.stringify(value));
    }
    
    // 不同数据的缓存时间
    readonly TTL = {
        PRICE: 10,          // 价格：10秒
        VOLUME: 60,         // 交易量：1分钟
        LIQUIDITY: 300,     // 流动性：5分钟
        HONEYPOT: 3600,     // 貔貅检测：1小时
        SMART_MONEY: 86400, // 聪明钱地址：1天
    };
}
```

### 4.2 事件驱动架构

```typescript
// event-bus.ts
import { EventEmitter } from 'events';

export class EventBus extends EventEmitter {
    private static instance: EventBus;
    
    static getInstance(): EventBus {
        if (!this.instance) {
            this.instance = new EventBus();
            this.instance.setMaxListeners(100);
        }
        return this.instance;
    }
    
    // 事件类型
    static readonly EVENTS = {
        NEW_SWAP: 'new_swap',
        STRATEGY_SIGNAL: 'strategy_signal',
        TRADE_DECISION: 'trade_decision',
        TRADE_EXECUTED: 'trade_executed',
        POSITION_UPDATED: 'position_updated',
        PNL_UPDATED: 'pnl_updated',
        ERROR: 'error',
    };
}

// 使用示例
const eventBus = EventBus.getInstance();

// 网关发送事件
eventBus.emit(EventBus.EVENTS.NEW_SWAP, swapData);

// 策略监听事件
eventBus.on(EventBus.EVENTS.NEW_SWAP, async (swap) => {
    await strategyProcessor.process(swap);
});
```

### 4.3 监控和日志

```typescript
// monitoring.ts
import winston from 'winston';

export class MonitoringService {
    private logger: winston.Logger;
    private metrics: Map<string, any> = new Map();
    
    constructor() {
        this.logger = winston.createLogger({
            level: 'info',
            format: winston.format.json(),
            transports: [
                new winston.transports.File({ filename: 'error.log', level: 'error' }),
                new winston.transports.File({ filename: 'combined.log' }),
                new winston.transports.Console({
                    format: winston.format.simple()
                })
            ]
        });
    }
    
    // 记录交易
    logTrade(trade: any): void {
        this.logger.info('Trade executed', {
            token: trade.token,
            action: trade.action,
            amount: trade.amount,
            price: trade.price,
            pnl: trade.pnl,
            timestamp: Date.now()
        });
    }
    
    // 性能监控
    trackPerformance(operation: string, duration: number): void {
        const key = `perf:${operation}`;
        const current = this.metrics.get(key) || { count: 0, total: 0 };
        
        current.count++;
        current.total += duration;
        current.avg = current.total / current.count;
        
        this.metrics.set(key, current);
        
        // 如果延迟过高，记录警告
        if (duration > 100) {
            this.logger.warn(`High latency detected`, {
                operation,
                duration,
                timestamp: Date.now()
            });
        }
    }
    
    // 获取系统状态
    getSystemStats(): any {
        return {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            metrics: Object.fromEntries(this.metrics),
            timestamp: Date.now()
        };
    }
}
```

## 五、启动流程

```typescript
// main.ts
import { GatewayService } from './gateway/gateway-service';
import { StrategyProcessor } from './strategy/strategy-processor';
import { DecisionService } from './decision/decision-service';
import { TradeService } from './trade/trade-service';
import { OrderService } from './orders/order-service';
import { MonitoringService } from './monitoring/monitoring';

async function startBot() {
    console.log('🚀 启动MEV交易机器人...');
    
    // 1. 初始化服务
    const gateway = new GatewayService();
    const strategy = new StrategyProcessor();
    const decision = new DecisionService();
    const trade = new TradeService();
    const order = new OrderService();
    const monitoring = new MonitoringService();
    
    // 2. 连接事件流
    const eventBus = EventBus.getInstance();
    
    // 网关 -> 策略
    gateway.on('swap', async (swap) => {
        await strategy.processSwap(swap);
    });
    
    // 策略 -> 决策
    strategy.on('signal', async (signal) => {
        const decision = await decision.processSignal(signal);
        if (decision.action !== 'REJECT') {
            eventBus.emit('trade_decision', decision);
        }
    });
    
    // 决策 -> 交易
    eventBus.on('trade_decision', async (decision) => {
        const order = await order.createOrder(decision);
        const result = await trade.executeTrade(decision);
        await order.updatePosition(order, result);
        monitoring.logTrade({ ...order, ...result });
    });
    
    // 3. 启动监控API
    const app = express();
    app.get('/stats', (req, res) => {
        res.json({
            system: monitoring.getSystemStats(),
            positions: order.getPositionStats(),
        });
    });
    
    app.listen(3000, () => {
        console.log('📊 监控面板: http://localhost:3000');
    });
    
    // 4. 优雅关闭
    process.on('SIGINT', async () => {
        console.log('正在关闭...');
        
        // 平掉所有仓位
        const positions = await order.getPositionStats();
        for (const position of positions.positions) {
            await trade.executeTrade({
                action: 'SELL',
                token: position.token,
                amount: position.amount,
                // ...
            });
        }
        
        process.exit(0);
    });
    
    console.log('✅ MEV机器人启动成功！');
}

// 启动
startBot().catch(console.error);
```

## 六、部署和运维

### 6.1 PM2配置

```javascript
// ecosystem.config.js
module.exports = {
    apps: [{
        name: 'mev-bot',
        script: './dist/main.js',
        instances: 1,
        exec_mode: 'fork',
        env: {
            NODE_ENV: 'production',
            RPC_URL: 'wss://mainnet.infura.io/ws/v3/YOUR_KEY',
            PRIVATE_KEY: 'YOUR_PRIVATE_KEY',
            REDIS_URL: 'redis://localhost:6379',
        },
        error_file: './logs/err.log',
        out_file: './logs/out.log',
        log_date_format: 'YYYY-MM-DD HH:mm:ss',
        max_memory_restart: '2G',
        
        // 自动重启
        autorestart: true,
        watch: false,
        max_restarts: 10,
        min_uptime: '10s',
        
        // 监控
        monitoring: true,
    }]
};
```

### 6.2 Docker部署

```dockerfile
# Dockerfile
FROM node:20-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 复制代码
COPY dist ./dist

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node healthcheck.js

# 运行
CMD ["node", "dist/main.js"]
```

### 6.3 监控脚本

```bash
#!/bin/bash
# monitor.sh

# 检查进程
check_process() {
    if ! pgrep -f "mev-bot" > /dev/null; then
        echo "MEV Bot is down! Restarting..."
        pm2 restart mev-bot
        
        # 发送告警
        curl -X POST https://api.telegram.org/bot$BOT_TOKEN/sendMessage \
            -d "chat_id=$CHAT_ID" \
            -d "text=⚠️ MEV Bot重启"
    fi
}

# 检查性能
check_performance() {
    response=$(curl -s http://localhost:3000/stats)
    avg_latency=$(echo $response | jq '.metrics.perf.avg')
    
    if (( $(echo "$avg_latency > 50" | bc -l) )); then
        echo "High latency detected: ${avg_latency}ms"
        # 发送告警
    fi
}

# 检查盈亏
check_pnl() {
    response=$(curl -s http://localhost:3000/stats)
    pnl=$(echo $response | jq '.positions.realizedPnL')
    
    if (( $(echo "$pnl < -1000" | bc -l) )); then
        echo "Large loss detected: $${pnl}"
        # 停止交易
        pm2 stop mev-bot
    fi
}

# 主循环
while true; do
    check_process
    check_performance
    check_pnl
    sleep 60
done
```

## 七、优化建议

### 7.1 性能优化
- 使用 `worker_threads` 并行处理策略
- 批量处理交易，减少数据库写入
- 使用连接池管理WebSocket连接
- 预计算常用数据，减少重复计算

### 7.2 策略优化
- A/B测试不同的权重配置
- 根据历史数据回测优化阈值
- 动态调整策略权重
- 增加机器学习模型（可选）

### 7.3 风险控制
- 设置每日最大亏损限制
- 实施分级止损机制
- 监控异常交易模式
- 定期审计交易记录

## 八、总结

这个简化版架构的特点：

✅ **纯TypeScript实现**：易于开发和维护
✅ **事件驱动架构**：响应快速，易于扩展
✅ **简单评分系统**：4个策略加权评分，逻辑清晰
✅ **模块化设计**：各模块职责明确，便于调试
✅ **易于部署**：使用PM2或Docker即可部署

核心优势：
- 开发速度快
- 维护成本低
- 易于调试和优化
- 可以快速迭代

这个架构完全满足您的需求，不涉及复杂计算，专注于事件驱动的评分机制，TypeScript完全够用！