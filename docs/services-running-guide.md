# 服务运行指南

本项目采用微服务架构，每个服务都可以独立运行，也可以在主应用中集成运行。

## 🚀 独立运行服务

### 开发模式（推荐用于开发和调试）

```bash
# 运行单个服务（开发模式，支持热重载）
npm run dev:gateway      # 网关服务
npm run dev:strategy     # 策略服务
npm run dev:decision     # 决策服务
npm run dev:trade        # 交易服务
npm run dev:orders       # 订单服务
npm run dev:chains       # 链数据服务
```

### 生产模式

```bash
# 先构建所有服务
npm run build:services

# 运行单个服务（生产模式）
npm run start:gateway    # 网关服务
npm run start:strategy   # 策略服务
npm run start:decision   # 决策服务
npm run start:trade      # 交易服务
npm run start:orders     # 订单服务
npm run start:chains     # 链数据服务
```

### 直接在服务目录运行

```bash
# 进入服务目录
cd services/orders

# 开发模式
npm run dev

# 生产模式
npm run build && npm run start
```

## 🏗️ 集成运行（主应用）

```bash
# 运行完整的 MEV Bot 系统
npm run dev:main
```

## 📋 服务说明

### 1. Gateway 服务 (`services/gateway`)
- **功能**: 区块链数据接入和转换
- **端口**: 无固定端口（数据处理服务）
- **依赖**: Redis
- **独立运行**: ✅ 支持
- **说明**: 监听区块链事件，转换数据格式并发布到 Redis

### 2. Strategy 服务 (`services/strategy`)
- **功能**: 交易策略分析
- **端口**: 无固定端口（数据处理服务）
- **依赖**: Redis, Gateway 服务
- **独立运行**: ✅ 支持
- **说明**: 接收交易数据，执行策略分析，输出决策参数

### 3. Decision 服务 (`services/decision`)
- **功能**: 交易决策
- **端口**: 无固定端口（数据处理服务）
- **依赖**: Redis
- **独立运行**: ✅ 支持
- **说明**: 接收策略分析结果，做出交易决策

### 4. Trade 服务 (`services/trade`)
- **功能**: 交易执行
- **端口**: 无固定端口（交易服务）
- **依赖**: Solana RPC
- **独立运行**: ✅ 支持
- **说明**: 执行具体的区块链交易操作

### 5. Orders 服务 (`services/orders`)
- **功能**: 订单管理
- **端口**: 无固定端口（数据处理服务）
- **依赖**: Redis
- **独立运行**: ✅ 支持
- **说明**: 管理交易订单的生命周期

### 6. Chains 服务 (`services/chains`)
- **功能**: 区块链数据收集
- **端口**: 无固定端口（数据收集服务）
- **依赖**: Redis, GRPC 端点
- **独立运行**: ✅ 支持
- **说明**: 实时收集区块链数据，支持演示模式

## 🔧 配置管理

每个服务都支持独立的配置管理：

### 1. 统一配置（推荐）
在项目根目录创建 `.env` 文件：
```bash
# 根目录 .env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
DEV_REDIS_HOST=127.0.0.1
DEV_REDIS_PORT=6379
```

### 2. 服务独立配置
在服务目录创建 `.env` 文件：
```bash
# services/orders/.env
ORDERS_REDIS_HOST=orders-redis.example.com
ORDERS_REDIS_DB=2
```

## 🐛 调试和监控

### 查看服务日志
```bash
# 开发模式会显示详细日志
npm run dev:orders

# 生产模式日志较少
npm run start:orders
```

### 服务健康检查
每个服务启动时会显示初始化状态：
- ✅ 服务初始化成功
- ⚠️ 警告信息（如 Redis 连接失败）
- ❌ 错误信息

## 🔄 服务间通信

服务间通过 Redis 发布订阅模式通信：

```
Gateway → Strategy → Decision → Trade
   ↓         ↓         ↓        ↓
Orders ← Orders ← Orders ← Orders
```

### 主要通信频道
- `swap_events`: 交易事件
- `strategy_results`: 策略分析结果
- `decision_results`: 决策结果
- `trade_events`: 交易事件
- `order_events`: 订单事件

## 📦 部署建议

### 开发环境
- 使用 `npm run dev:*` 命令
- 启用详细日志
- 使用本地 Redis

### 生产环境
- 使用 `npm run start:*` 命令
- 关闭详细日志
- 使用集群 Redis
- 配置环境变量
- 使用进程管理器（如 PM2）

### Docker 部署
每个服务都可以独立容器化：
```dockerfile
FROM node:18
WORKDIR /app
COPY services/orders/ .
RUN npm install && npm run build
CMD ["npm", "start"]
```

## 🚨 故障排除

### 常见问题

1. **Redis 连接失败**
   - 检查 Redis 服务是否启动
   - 检查环境变量配置
   - 查看网络连接

2. **服务启动失败**
   - 检查依赖是否安装
   - 检查 TypeScript 编译错误
   - 查看端口占用情况

3. **服务间通信失败**
   - 检查 Redis 发布订阅配置
   - 确认频道名称正确
   - 检查消息格式

### 日志级别
- `console.log`: 一般信息
- `console.warn`: 警告信息
- `console.error`: 错误信息
