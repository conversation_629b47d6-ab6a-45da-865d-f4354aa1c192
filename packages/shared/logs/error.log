{"level":"error","message":"数据库连接失败","stack":"Error: Connection timeout after 5000ms\n    at testLogger (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:15:38)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:74:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:06:12"}
{"level":"error","message":"无颜色的错误信息","stack":"Error: 测试错误\n    at testLogger (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:45:39)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:74:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:06:12"}
{"level":"error","message":"捕获到异常","stack":"Error: 模拟的业务逻辑错误\n    at testLogger (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:20:15)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:74:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:06:12"}
{"context":"Redis","level":"error","message":"Redis 连接断开","stack":"Error: Connection lost\n    at testLogger (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:32:37)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-test.js:74:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:06:12"}
{"level":"error","message":"这条错误会显示","timestamp":"2025-09-04 16:06:12"}
{"level":"error","message":"数据库连接失败","stack":"Error: Connection timeout after 5000ms\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:14:38)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:40:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:04"}
{"level":"error","message":"这是无颜色的错误","stack":"Error: Test error\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:31:39)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:40:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:04"}
{"context":"Database","level":"error","message":"查询失败","stack":"Error: Syntax error in SQL\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:22:28)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:40:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:04"}
{"context":"API","level":"error","message":"API 请求失败","stack":"Error: Rate limit exceeded\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:26:33)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:40:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:04"}
{"level":"error","message":"数据库连接失败","stack":"Error: Connection timeout after 5000ms\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:16:38)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:42:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:30"}
{"level":"error","message":"这是无颜色的错误","stack":"Error: Test error\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:33:39)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:42:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:30"}
{"context":"Database","level":"error","message":"查询失败","stack":"Error: Syntax error in SQL\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:24:28)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:42:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:30"}
{"context":"API","level":"error","message":"API 请求失败","stack":"Error: Rate limit exceeded\n    at demoColorfulLogs (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:28:33)\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/logger-demo.js:42:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:08:30"}
{"level":"error","message":"这是错误信息，文字应该是默认颜色","stack":"Error: 测试错误\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/test-colors.js:10:43)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:11:22"}
{"level":"error","message":"❌ Error with object:","stack":"Error: Test error\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/test-object-color.js:17:47)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:13:43"}
{"level":"error","message":"这是错误信息 - 应该是鲜艳的大红色","stack":"Error: 测试错误 - 堆栈也应该是大红色\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/test-new-colors.js:9:45)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:16:15"}
{"level":"error","message":"错误信息","stack":"Error: 测试错误\n    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/test-milliseconds.js:9:31)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)\n    at node:internal/main/run_main_module:36:49","timestamp":"2025-09-04 16:18:46.099"}
2025-09-04 16:23:06.778 [ERROR] 错误信息 - 应该输出到文件
Error: 测试错误
    at Object.<anonymous> (/Users/<USER>/code/myProject/QuantumChain/packages/shared/dist/utils/test-file-output.js:15:41)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:171:5)
    at node:internal/main/run_main_module:36:49
