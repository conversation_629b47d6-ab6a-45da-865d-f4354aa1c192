
/**
 * Redis 配置接口
 */
export interface RedisConfig {
    host?: string;
    port?: number;
    password?: string;
    db?: number;
    retryStrategy?: (times: number) => number;
    maxRetriesPerRequest?: number | null;
    enableReadyCheck?: boolean;
    reconnectOnError?: (err: Error) => boolean;
}

/**
 * Redis 服务配置接口
 */
export interface RedisServiceConfig {
    redis?: RedisConfig;
    isDev?: boolean;
    enableLogging?: boolean;
}

/**
 * 策略分数模型
 */
export interface StrategyScoreResult {
    strategyType: string;
    buyScore: number;
    sellScore: number;
}

/**
 * 决策分数模型
 */
export interface DecisionScoreParam {
    priceTrend: StrategyScoreResult;
    smartMoney: StrategyScoreResult;
    tradingVolume: StrategyScoreResult;
}

/**
 * 策略类型
 */
export enum StrategyType {

    /**
     * 价格趋势
     */
    PRICE_TREND,

    /**
     * 聪明钱
     */
    SMART_MONEY,

    /**
     * 社交信息
     */
    SOCIALIZING_INFORMATION,

    /**
     * 交易量
     */
    TRADING_VOLUME
}