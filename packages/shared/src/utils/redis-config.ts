import {RedisConfig, RedisServiceConfig} from "../types/types";
import * as path from 'path';

/**
 * Redis 配置管理器
 */
export class RedisConfigManager {
    /**
     * 从环境变量创建 Redis 配置
     * @param isDev 是否为开发环境
     * @param envPath 可选的 .env 文件路径
     */
    public static createFromEnv(envPath?: string): RedisServiceConfig {
        // 如果指定了 .env 文件路径，则加载它
        if (envPath) {
            this.loadEnvFile(envPath);
        }

        const redisConfig: RedisConfig = {
            host: process.env[`REDIS_HOST`] || "127.0.0.1",
            port: parseInt(process.env[`REDIS_PORT`] || "6379"),
            password: process.env[`REDIS_PASSWORD`] || undefined,
            db: parseInt(process.env[`REDIS_DB`] || "0"),
            retryStrategy: (times) => Math.min(times * 50, 2000),
            maxRetriesPerRequest: null,
            enableReadyCheck: true,
            reconnectOnError: (err) => {
                const targetError = 'READONLY';
                return err.message.includes(targetError);
            },
        };

        return {
            redis: redisConfig,
            enableLogging: process.env.REDIS_ENABLE_LOGGING === 'true'
        };
    }

    /**
     * 创建开发环境配置
     */
    public static createDevConfig(): RedisServiceConfig {
        return {
            redis: {
                host: "127.0.0.1",
                port: 6379,
                password: undefined,
                db: 0,
            },
            isDev: true,
            enableLogging: true,
        };
    }

    /**
     * 创建生产环境配置
     * @param host Redis 主机
     * @param port Redis 端口
     * @param password Redis 密码
     * @param db Redis 数据库
     */
    public static createProdConfig(
        host: string,
        port: number,
        password?: string,
        db: number = 0
    ): RedisServiceConfig {
        return {
            redis: {
                host,
                port,
                password,
                db,
                retryStrategy: (times) => Math.min(times * 100, 5000),
                maxRetriesPerRequest: 3,
                enableReadyCheck: true,
                reconnectOnError: (err) => {
                    const targetErrors = ['READONLY', 'ECONNRESET', 'ENOTFOUND'];
                    return targetErrors.some(error => err.message.includes(error));
                },
            },
            isDev: false,
            enableLogging: false,
        };
    }

    /**
     * 创建测试环境配置
     */
    public static createTestConfig(): RedisServiceConfig {
        return {
            redis: {
                host: "127.0.0.1",
                port: 6379,
                password: undefined,
                db: 15, // 使用不同的数据库避免冲突
            },
            isDev: true,
            enableLogging: false, // 测试时不输出日志
        };
    }

    /**
     * 验证 Redis 配置
     * @param config Redis 服务配置
     */
    public static validateConfig(config: RedisServiceConfig): boolean {
        if (!config.redis) {
            return false;
        }

        const {host, port} = config.redis;

        if (!host || typeof host !== 'string') {
            return false;
        }

        if (!port || typeof port !== 'number' || port < 1 || port > 65535) {
            return false;
        }

        return true;
    }

    /**
     * 加载 .env 文件
     * @param envPath .env 文件路径
     */
    private static loadEnvFile(envPath: string): void {
        try {
            // 动态导入 dotenv，避免在不需要时加载
            const dotenv = require('dotenv');
            const result = dotenv.config({path: envPath});

            if (result.error) {
                console.warn(`Failed to load .env file from ${envPath}:`, result.error.message);
            }
        } catch (error) {
            console.warn(`dotenv not available, skipping .env file loading:`, error);
        }
    }

    /**
     * 自动查找并加载 .env 文件
     * @param startPath 开始查找的路径
     */
    public static autoLoadEnv(startPath: string = process.cwd()): void {
        // 构建可能的 .env 文件路径（按优先级排序）
        const envPaths = [
            path.join(startPath, '.env'),
            path.join(startPath, '..', '.env'),
            path.join(startPath, '..', '..', '.env'),
            path.join(process.cwd(), '.env'),
        ];

        for (const envPath of envPaths) {
            try {
                const fs = require('fs');
                if (fs.existsSync(envPath)) {
                    this.loadEnvFile(envPath);
                    console.log(`Loaded .env file from: ${envPath}`);
                    return;
                }
            } catch (error) {
                // 继续查找下一个路径
            }
        }

        console.log('No .env file found, using system environment variables');
    }
}

/**
 * 便捷函数：从环境变量创建配置
 * @param envPath 可选的 .env 文件路径
 */
export const createRedisConfigFromEnv = (envPath?: string): RedisServiceConfig =>
    RedisConfigManager.createFromEnv(envPath);

/**
 * 便捷函数：创建开发配置
 */
export const createDevRedisConfig = (): RedisServiceConfig =>
    RedisConfigManager.createDevConfig();

/**
 * 便捷函数：创建生产配置
 */
export const createProdRedisConfig = (
    host: string,
    port: number,
    password?: string,
    db: number = 0
): RedisServiceConfig =>
    RedisConfigManager.createProdConfig(host, port, password, db);

/**
 * 便捷函数：创建测试配置
 */
export const createTestRedisConfig = (): RedisServiceConfig =>
    RedisConfigManager.createTestConfig();

/**
 * 便捷函数：自动加载 .env 文件
 * @param startPath 开始查找的路径
 */
export const autoLoadEnv = (startPath?: string): void =>
    RedisConfigManager.autoLoadEnv(startPath);
