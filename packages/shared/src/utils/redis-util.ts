import Redis, {RedisOptions} from "ioredis";
import { RedisConfig, RedisServiceConfig } from "../types/types";

/**
 * Redis 工具类 - 支持配置传入的单例模式
 */
export class RedisUtil {
    private static instance: RedisUtil;
    private readonly redis: Redis;
    private readonly config: RedisServiceConfig;

    private constructor(config: RedisServiceConfig = {}) {
        this.config = config;

        // 默认配置
        const defaultRedisConfig: RedisConfig = {
            host: "127.0.0.1",
            port: 6379,
            password: undefined,
            db: 0,
            retryStrategy: (times) => Math.min(times * 50, 2000),
            maxRetriesPerRequest: null,
            enableReadyCheck: true,
            reconnectOnError: (err) => {
                const targetError = 'READONLY';
                return err.message.includes(targetError);
            },
        };

        // 合并配置
        const redisConfig = { ...defaultRedisConfig, ...config.redis };

        const redisOptions: RedisOptions = {
            host: redisConfig.host,
            port: redisConfig.port,
            password: redisConfig.password,
            db: redisConfig.db,
            retryStrategy: redisConfig.retryStrategy,
            maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
            enableReadyCheck: redisConfig.enableReadyCheck,
            reconnectOnError: redisConfig.reconnectOnError,
        };

        this.redis = new Redis(redisOptions);
        this.setupEventListeners();
    }

    private setupEventListeners() {
        if (!this.config.enableLogging) return;

        this.redis.on("error", (error) => {
            console.error(`Redis 错误: ${error}`);
        });

        this.redis.on("connect", () => {
            console.info("Redis 已连接");
        });

        this.redis.on("ready", () => {
            console.info("Redis 准备就绪");
        });

        this.redis.on("close", () => {
            console.info("Redis 连接已关闭");
        });

        this.redis.on("reconnecting", (time: number) => {
            console.warn(`Redis 正在重连，尝试次数: ${time}`);
        });

        this.redis.on("end", () => {
            console.info("Redis 连接已终止");
        });
    }

    /**
     * 获取单例实例
     * @param config Redis 服务配置
     */
    public static getInstance(config: RedisServiceConfig = {}): RedisUtil {
        if (!RedisUtil.instance) {
            RedisUtil.instance = new RedisUtil(config);
        }
        return RedisUtil.instance;
    }

    /**
     * 重置单例实例（用于测试或重新配置）
     */
    public static resetInstance(): void {
        if (RedisUtil.instance) {
            RedisUtil.instance.redis.disconnect();
            RedisUtil.instance = null as any;
        }
    }

    /**
     * 创建新的实例（非单例模式）
     * @param config Redis 服务配置
     */
    public static createInstance(config: RedisServiceConfig = {}): RedisUtil {
        return new RedisUtil(config);
    }

    public getRedisClient(): Redis {
        return this.redis;
    }

    /**
     * 获取分布式锁
     * @param lockKey 锁的键
     * @param lockValue 锁的值
     * @param options 锁选项
     * @returns 锁对象，如果获取失败返回null
     */
    public async getLock(
        lockKey: string,
        lockValue: string,
        options: {
            ttl?: number;
            waitTime?: number;
            retryInterval?: number;
            retryTimes?: number;
        } = {}
    ): Promise<{
        unlock: () => Promise<void>;
        extend: (duration: number) => Promise<boolean>;
    } | null> {
        const {
            ttl = 30,
            waitTime = 5000,
            retryInterval = 100,
            retryTimes = 3
        } = options;

        let acquired = false;
        let retries = 0;
        const startTime = Date.now();

        while (!acquired && retries < retryTimes && (Date.now() - startTime) < waitTime) {
            acquired = await this.tryAcquireLock(lockKey, lockValue, ttl);
            if (!acquired && retries < retryTimes - 1) {
                retries++;
                await this.sleep(retryInterval);
            }
        }

        if (!acquired) {
            return null;
        }

        return {
            unlock: async () => {
                const released = await this.releaseLock(lockKey, lockValue);
                if (!released) {
                    throw new Error('Failed to release lock');
                }
            },
            extend: async (duration: number) => {
                const script = `
                    if redis.call("get",KEYS[1]) == ARGV[1] then
                        return redis.call("expire",KEYS[1],ARGV[2])
                    else
                        return 0
                    end
                `;

                try {
                    const result = await this.redis.eval(
                        script,
                        1,
                        lockKey,
                        lockValue,
                        duration.toString()
                    );
                    return result === 1;
                } catch (error) {
                    console.error(`延长锁时间失败 ${lockKey}:`, error);
                    return false;
                }
            }
        };
    }


    /**
     * 尝试获取锁
     */
    private async tryAcquireLock(
        key: string,
        value: string,
        ttl: number
    ): Promise<boolean> {
        // 使用setNX命令
        const locked = await this.redis.setnx(key, value);
        if (locked) {
            // 如果成功获取锁，设置过期时间
            await this.redis.expire(key, ttl);
        }
        return locked === 1;
    }

    /**
     * 释放锁（使用Lua脚本确保原子性）
     */
    /**
     * 释放锁（使用Lua脚本确保原子性）
     */
    private async releaseLock(key: string, value: string): Promise<boolean> {
        const script = `
            if redis.call("get",KEYS[1]) == ARGV[1] then
                return redis.call("del",KEYS[1])
            else
                return 0
            end
        `;

        try {
            const result = await this.redis.eval(script, 1, key, value);
            return result === 1;
        } catch (error) {
            console.error(`释放锁失败 ${key}:`, error);
            return false;
        }
    }

    /**
     * 延长锁时间（使用Lua脚本确保原子性）
     */
    private async extendLock(
        key: string,
        value: string,
        duration: number
    ): Promise<boolean> {
        const script = `
            if redis.call("get",KEYS[1]) == ARGV[1] then
                return redis.call("expire",KEYS[1],ARGV[2])
            else
                return 0
            end
        `;
        const result = await this.redis.eval(
            script,
            1,
            key,
            value,
            duration.toString()
        );

        return result === 1;
    }

    /**
     * 检查锁是否存在
     */
    public async isLocked(key: string): Promise<boolean> {
        const exists = await this.redis.exists(key);
        return exists === 1;
    }


    /**
     * 批量检查多个key的锁状态
     * @param keys 要检查的key数组
     * @returns 返回被锁定的key数组
     */
    public async getLockedKeys(keys: string[]): Promise<string[]> {
        if (!keys || keys.length === 0) {
            return [];
        }

        try {
            // 使用pipeline批量查询
            const pipeline = this.redis.pipeline();
            keys.forEach(key => pipeline.exists(key));

            const results = await pipeline.exec();
            if (!results) {
                return [];
            }

            // 过滤出被锁定的key
            const lockedKeys = keys.filter((key, index) => {
                const [err, exists] = results[index];
                return !err && exists === 1;  // exists === 1 表示key存在，即被锁定
            });

            return lockedKeys;
        } catch (error) {
            console.error('批量检查锁状态失败:', error);
            throw error;
        }
    }

    /**
     * 获取锁的TTL
     */
    public async getLockTTL(key: string): Promise<number> {
        return this.redis.ttl(key);
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

/**
 * 获取 Redis 工具类实例（单例模式）
 * @param config Redis 服务配置
 */
export const getRedisUtil = (config: RedisServiceConfig = {}): RedisUtil => RedisUtil.getInstance(config);

/**
 * 创建新的 Redis 工具类实例（非单例模式）
 * @param config Redis 服务配置
 */
export const createRedisUtil = (config: RedisServiceConfig = {}): RedisUtil => RedisUtil.createInstance(config);
