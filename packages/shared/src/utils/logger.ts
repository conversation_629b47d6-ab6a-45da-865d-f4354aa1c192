import winston from 'winston';

// 自定义控制台日志格式，带日期前缀
const consoleFormat = winston.format.printf(({ level, message, timestamp, ...meta }) => {
    const metaString = Object.keys(meta).length ? JSON.stringify(meta) : '';
    return `${timestamp} [${level.toUpperCase()}] ${message} ${metaString}`;
});

// 定义 Winston 日志配置
const logger = winston.createLogger({
    level: 'info', // 默认日志级别
    format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), // 添加时间戳
        winston.format.errors({ stack: true }) // 捕获错误堆栈
    ),
    transports: [
        // 控制台输出：带日期前缀的格式化日志
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(), // 彩色输出
                consoleFormat // 自定义格式
            )
        ),
        // 文件输出：JSON 格式（所有日志）
        new winston.transports.File({
            filename: 'logs/combined.log',
            format: winston.format.json() // 文件保持 JSON 格式
        }),
        // 文件输出：JSON 格式（仅错误日志）
        new winston.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            format: winston.format.json()
        })
    ]
});

// 导出一个简单的日志工具类，方便在项目中使用
export class Logger {
    // 记录信息日志
    static info(message: string, meta?: any) {
        logger.info(message, meta);
    }

    // 记录错误日志
    static error(message: string, meta?: any) {
        logger.error(message, meta);
    }

    // 记录警告日志
    static warn(message: string, meta?: any) {
        logger.warn(message, meta);
    }

    // 记录调试日志
    static debug(message: string, meta?: any) {
        logger.debug(message, meta);
    }

    // 为特定模块创建子日志记录器，带上下文
    static child(context: string) {
        return logger.child({ context });
    }
}