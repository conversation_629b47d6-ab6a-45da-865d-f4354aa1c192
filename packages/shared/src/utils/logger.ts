/**
 * 日志级别枚举
 */
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
    level?: LogLevel;
    enableColors?: boolean;
    enableTimestamp?: boolean;
    dateFormat?: string;
}

/**
 * 简单高效的日志工具类
 * 支持彩色输出、时间戳、错误堆栈跟踪
 */
export class Logger {
    private static config: LoggerConfig = {
        level: LogLevel.INFO,
        enableColors: true,
        enableTimestamp: true,
        dateFormat: 'YYYY-MM-DD HH:mm:ss'
    };

    // ANSI 颜色代码
    private static readonly colors = {
        reset: '\x1b[0m',
        bright: '\x1b[1m',
        dim: '\x1b[2m',
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m',
        white: '\x1b[37m',
        gray: '\x1b[90m'
    };

    /**
     * 配置日志器
     */
    static configure(config: Partial<LoggerConfig>): void {
        this.config = { ...this.config, ...config };
    }

    /**
     * 格式化时间戳
     */
    private static formatTimestamp(): string {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    /**
     * 格式化日志消息
     */
    private static formatMessage(level: string, message: string, color: string): string {
        const timestamp = this.config.enableTimestamp ? this.formatTimestamp() : '';
        const colorStart = this.config.enableColors ? color : '';
        const colorEnd = this.config.enableColors ? this.colors.reset : '';

        const parts = [];
        if (timestamp) {
            parts.push(`${this.config.enableColors ? this.colors.gray : ''}${timestamp}${colorEnd}`);
        }
        parts.push(`${colorStart}[${level}]${colorEnd}`);
        parts.push(message);

        return parts.join(' ');
    }

    /**
     * 格式化元数据
     */
    private static formatMeta(meta?: any): string {
        if (!meta) return '';

        if (typeof meta === 'string') {
            return ` ${meta}`;
        }

        if (meta instanceof Error) {
            return `\n${this.config.enableColors ? this.colors.red : ''}${meta.stack || meta.message}${this.config.enableColors ? this.colors.reset : ''}`;
        }

        try {
            return ` ${JSON.stringify(meta, null, 2)}`;
        } catch (err) {
            return ` [Circular Object]`;
        }
    }

    /**
     * 通用日志方法
     */
    private static log(level: LogLevel, levelName: string, color: string, message: string, meta?: any): void {
        if (level < this.config.level!) return;

        const formattedMessage = this.formatMessage(levelName, message, color);
        const formattedMeta = this.formatMeta(meta);

        console.log(formattedMessage + formattedMeta);
    }

    /**
     * 调试日志
     */
    static debug(message: string, meta?: any): void {
        this.log(LogLevel.DEBUG, 'DEBUG', this.colors.cyan, message, meta);
    }

    /**
     * 信息日志
     */
    static info(message: string, meta?: any): void {
        this.log(LogLevel.INFO, 'INFO', this.colors.green, message, meta);
    }

    /**
     * 警告日志
     */
    static warn(message: string, meta?: any): void {
        this.log(LogLevel.WARN, 'WARN', this.colors.yellow, message, meta);
    }

    /**
     * 错误日志 - 自动处理 Error 对象的堆栈跟踪
     */
    static error(message: string, error?: Error | any): void {
        if (error instanceof Error) {
            this.log(LogLevel.ERROR, 'ERROR', this.colors.red, message, error);
        } else {
            this.log(LogLevel.ERROR, 'ERROR', this.colors.red, message, error);
        }
    }

    /**
     * 成功日志（特殊的 info 日志）
     */
    static success(message: string, meta?: any): void {
        this.log(LogLevel.INFO, 'SUCCESS', this.colors.bright + this.colors.green, message, meta);
    }

    /**
     * 创建带上下文的日志器
     */
    static child(context: string) {
        return {
            debug: (message: string, meta?: any) => this.debug(`[${context}] ${message}`, meta),
            info: (message: string, meta?: any) => this.info(`[${context}] ${message}`, meta),
            warn: (message: string, meta?: any) => this.warn(`[${context}] ${message}`, meta),
            error: (message: string, error?: Error | any) => this.error(`[${context}] ${message}`, error),
            success: (message: string, meta?: any) => this.success(`[${context}] ${message}`, meta)
        };
    }

    /**
     * 设置日志级别
     */
    static setLevel(level: LogLevel): void {
        this.config.level = level;
    }

    /**
     * 启用/禁用颜色输出
     */
    static setColors(enabled: boolean): void {
        this.config.enableColors = enabled;
    }

    /**
     * 启用/禁用时间戳
     */
    static setTimestamp(enabled: boolean): void {
        this.config.enableTimestamp = enabled;
    }
}

// 使用示例：
// import { Logger, LogLevel } from './logger';
//
// // 基本使用
// Logger.info('服务启动成功', { port: 3000 });
// Logger.warn('连接超时，正在重试...');
// Logger.error('数据库连接失败', new Error('Connection timeout'));
// Logger.success('订单处理完成', { orderId: '12345' });
//
// // 配置日志器
// Logger.configure({ level: LogLevel.DEBUG, enableColors: false });
//
// // 使用上下文日志器
// const dbLogger = Logger.child('Database');
// dbLogger.info('连接已建立');
// dbLogger.error('查询失败', new Error('Syntax error'));