import winston from 'winston';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 日志配置接口
 */
export interface LoggerConfig {
    level?: string;
    enableFileLogging?: boolean;
    logDir?: string;
    enableColors?: boolean;
    dateFormat?: string;
}

/**
 * 基于 Winston 的增强日志工具类
 * 支持彩色输出、时间戳、错误堆栈跟踪、文件输出
 */
export class Logger {
    private static winstonLogger: winston.Logger;
    private static config: LoggerConfig = {
        level: 'info',
        enableFileLogging: true,
        logDir: 'logs',
        enableColors: true,
        dateFormat: 'YYYY-MM-DD HH:mm:ss.SSS'
    };

    /**
     * 初始化 Winston 日志器
     */
    private static initializeLogger(): void {
        if (this.winstonLogger) return;

        // 获取调用者的工作目录作为日志输出目录
        const callerDir = process.cwd();
        const logDir = this.config.logDir ? path.join(callerDir, this.config.logDir) : path.join(callerDir, 'logs');

        // 确保日志目录存在
        if (this.config.enableFileLogging) {
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
            }
        }

        // 自定义控制台格式 - 带彩色日期前缀和错误堆栈
        const consoleFormat = winston.format.printf(({ level, message, stack, ...meta }) => {
            // 生成精确到毫秒的时间戳
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
            const timestamp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;

            // 定义颜色代码
            const colors = {
                reset: '\x1b[0m',
                bright: '\x1b[1m',
                dim: '\x1b[2m',
                red: '\x1b[31m',
                brightRed: '\x1b[91m',  // 更鲜艳的红色
                green: '\x1b[32m',
                yellow: '\x1b[33m',
                blue: '\x1b[34m',
                magenta: '\x1b[35m',
                cyan: '\x1b[36m',
                white: '\x1b[37m',
                gray: '\x1b[90m',
                bgRed: '\x1b[41m',
                bgGreen: '\x1b[42m',
                bgYellow: '\x1b[43m',
                bgBlue: '\x1b[44m'
            };

            // 根据日志级别设置颜色和图标
            let levelColor = '';
            let levelIcon = '';

            switch (level.toLowerCase()) {
                case 'error':
                    levelColor = colors.bright + colors.brightRed;
                    levelIcon = '❌';
                    break;
                case 'warn':
                    levelColor = colors.bright + colors.yellow;
                    levelIcon = '⚠️ ';
                    break;
                case 'info':
                    levelColor = colors.bright + colors.green;
                    levelIcon = '💡';
                    break;
                case 'debug':
                    levelColor = colors.bright + colors.cyan;
                    levelIcon = '🔍';
                    break;
                default:
                    levelColor = colors.white;
                    levelIcon = '📄';
            }

            // 处理错误堆栈 - 使用鲜艳的红色
            let errorStack = '';
            if (stack) {
                errorStack = `\n${colors.brightRed}${stack}${colors.reset}`;
            }

            // 处理元数据
            const metaKeys = Object.keys(meta);
            let metaString = '';
            if (metaKeys.length > 0) {
                // 过滤掉 winston 内部字段
                const filteredMeta = Object.keys(meta)
                    .filter(key => !['level', 'message', 'timestamp', 'stack'].includes(key))
                    .reduce((obj, key) => {
                        obj[key] = meta[key];
                        return obj;
                    }, {} as any);

                if (Object.keys(filteredMeta).length > 0) {
                    metaString = `\n${JSON.stringify(filteredMeta, null, 2)}`;
                }
            }

            // 格式化时间戳 - 使用绿色
            const coloredTimestamp = `${colors.green}${timestamp}${colors.reset}`;

            // 格式化级别标签
            const coloredLevel = `${levelColor}[${level.toUpperCase()}]${colors.reset}`;

            // 消息保持默认颜色
            const coloredMessage = message;

            return `${coloredTimestamp} ${levelIcon} ${coloredLevel} ${coloredMessage}${metaString}${errorStack}`;
        });

        // 简单的无颜色格式
        const simpleFormat = winston.format.printf(({ level, message, stack, ...meta }) => {
            // 生成精确到毫秒的时间戳
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
            const timestamp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;

            // 处理错误堆栈
            let errorStack = '';
            if (stack) {
                errorStack = `\n${stack}`;
            }

            // 处理元数据
            const metaKeys = Object.keys(meta);
            let metaString = '';
            if (metaKeys.length > 0) {
                const filteredMeta = Object.keys(meta)
                    .filter(key => !['level', 'message', 'timestamp', 'stack'].includes(key))
                    .reduce((obj, key) => {
                        obj[key] = meta[key];
                        return obj;
                    }, {} as any);

                if (Object.keys(filteredMeta).length > 0) {
                    metaString = `\n${JSON.stringify(filteredMeta, null, 2)}`;
                }
            }

            return `${timestamp} [${level.toUpperCase()}] ${message}${metaString}${errorStack}`;
        });

        // 文件格式 - 和控制台一样但没有颜色和图标
        const fileFormat = winston.format.printf(({ level, message, stack, ...meta }) => {
            // 生成精确到毫秒的时间戳
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
            const timestamp = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;

            // 处理错误堆栈
            let errorStack = '';
            if (stack) {
                errorStack = `\n${stack}`;
            }

            // 处理元数据
            const metaKeys = Object.keys(meta);
            let metaString = '';
            if (metaKeys.length > 0) {
                const filteredMeta = Object.keys(meta)
                    .filter(key => !['level', 'message', 'timestamp', 'stack'].includes(key))
                    .reduce((obj, key) => {
                        obj[key] = meta[key];
                        return obj;
                    }, {} as any);

                if (Object.keys(filteredMeta).length > 0) {
                    metaString = `\n${JSON.stringify(filteredMeta, null, 2)}`;
                }
            }

            return `${timestamp} [${level.toUpperCase()}] ${message}${metaString}${errorStack}`;
        });

        // 创建传输器数组
        const transports: winston.transport[] = [
            // 控制台输出 - 根据配置选择彩色或简单格式
            new winston.transports.Console({
                format: winston.format.combine(
                    winston.format.errors({ stack: true }),
                    this.config.enableColors ? consoleFormat : simpleFormat
                )
            })
        ];

        // 添加文件输出
        if (this.config.enableFileLogging) {
            // 所有日志文件 - 使用和控制台一样的格式（无颜色）
            transports.push(new winston.transports.File({
                filename: path.join(logDir, 'common.log'),
                format: winston.format.combine(
                    winston.format.errors({ stack: true }),
                    fileFormat
                )
            }));

            // 错误日志文件 - 使用和控制台一样的格式（无颜色）
            transports.push(new winston.transports.File({
                filename: path.join(logDir, 'error.log'),
                level: 'error',
                format: winston.format.combine(
                    winston.format.errors({ stack: true }),
                    fileFormat
                )
            }));
        }

        // 创建 Winston 日志器
        this.winstonLogger = winston.createLogger({
            level: this.config.level,
            format: winston.format.combine(
                winston.format.timestamp({ format: this.config.dateFormat }),
                winston.format.errors({ stack: true })
            ),
            transports
        });
    }

    /**
     * 配置日志器
     */
    static configure(config: Partial<LoggerConfig>): void {
        this.config = { ...this.config, ...config };
        // 重新初始化日志器
        this.winstonLogger = null as any;
        this.initializeLogger();
    }

    /**
     * 获取 Winston 日志器实例
     */
    private static getLogger(): winston.Logger {
        if (!this.winstonLogger) {
            this.initializeLogger();
        }
        return this.winstonLogger;
    }

    /**
     * 调试日志
     */
    static debug(message: string, meta?: any): void {
        this.getLogger().debug(message, meta);
    }

    /**
     * 信息日志
     */
    static info(message: string, meta?: any): void {
        this.getLogger().info(message, meta);
    }

    /**
     * 警告日志
     */
    static warn(message: string, meta?: any): void {
        this.getLogger().warn(message, meta);
    }

    /**
     * 错误日志 - 自动处理 Error 对象的堆栈跟踪
     */
    static error(message: string, error?: Error | any): void {
        if (error instanceof Error) {
            this.getLogger().error(message, { stack: error.stack, ...error });
        } else {
            this.getLogger().error(message, error);
        }
    }

    /**
     * 成功日志（特殊的 info 日志）
     */
    static success(message: string, meta?: any): void {
        this.getLogger().info(`✅ ${message}`, meta);
    }

    /**
     * 创建带上下文的日志器
     */
    static child(context: string) {
        const childLogger = this.getLogger().child({ context });

        return {
            debug: (message: string, meta?: any) => childLogger.debug(message, meta),
            info: (message: string, meta?: any) => childLogger.info(message, meta),
            warn: (message: string, meta?: any) => childLogger.warn(message, meta),
            error: (message: string, error?: Error | any) => {
                if (error instanceof Error) {
                    childLogger.error(message, { stack: error.stack, ...error });
                } else {
                    childLogger.error(message, error);
                }
            },
            success: (message: string, meta?: any) => childLogger.info(`✅ ${message}`, meta)
        };
    }

    /**
     * 设置日志级别
     */
    static setLevel(level: string): void {
        this.config.level = level;
        if (this.winstonLogger) {
            this.winstonLogger.level = level;
        }
    }

    /**
     * 启用/禁用文件日志
     */
    static setFileLogging(enabled: boolean, logDir?: string): void {
        this.config.enableFileLogging = enabled;
        if (logDir) {
            this.config.logDir = logDir;
        }
        // 重新初始化日志器
        this.winstonLogger = null as any;
        this.initializeLogger();
    }

    /**
     * 启用/禁用颜色输出
     */
    static setColors(enabled: boolean): void {
        this.config.enableColors = enabled;
        // 重新初始化日志器
        this.winstonLogger = null as any;
        this.initializeLogger();
    }

    /**
     * 获取底层的 Winston 日志器实例（用于高级用法）
     */
    static getWinstonLogger(): winston.Logger {
        return this.getLogger();
    }
}

// 使用示例：
// import { Logger } from '@mev-bot/shared';
//
// // 基本使用 - 彩色日志输出（消息文本保持默认颜色）
// Logger.debug('调试信息', { userId: 123 });        // 🔍 [DEBUG] 标签青色
// Logger.info('服务启动成功', { port: 3000 });       // 💡 [INFO] 标签绿色
// Logger.warn('内存使用率较高', { usage: '85%' });    // ⚠️  [WARN] 标签黄色
// Logger.error('连接失败', new Error('timeout'));    // ❌ [ERROR] 标签大红色 + 堆栈
// Logger.success('处理完成', { orderId: '123' });    // 💡 [INFO] ✅ 标签绿色
//
// // 配置日志器
// Logger.configure({
//     level: 'debug',           // 日志级别: debug, info, warn, error
//     enableFileLogging: true,  // 启用文件日志
//     logDir: './logs',         // 日志文件目录
//     enableColors: true,       // 启用彩色输出
//     dateFormat: 'YYYY-MM-DD HH:mm:ss.SSS'  // 时间格式（精确到毫秒）
// });
//
// // 使用上下文日志器 - 自动添加上下文标识
// const dbLogger = Logger.child('Database');
// dbLogger.info('连接已建立');              // 会显示 [Database] 前缀
// dbLogger.error('查询失败', new Error('SQL syntax error'));
//
// const apiLogger = Logger.child('API');
// apiLogger.warn('请求超时', { endpoint: '/users', timeout: 5000 });
//
// // 动态配置
// Logger.setLevel('warn');        // 只显示 warn 和 error 级别
// Logger.setColors(false);        // 禁用彩色输出
// Logger.setFileLogging(true, './custom-logs');  // 启用文件日志到自定义目录
//
// // 获取底层 Winston 实例（高级用法）
// const winstonLogger = Logger.getWinstonLogger();
// winstonLogger.add(new winston.transports.File({ filename: 'custom.log' }));

// 使用示例：
// import { Logger, LogLevel } from './logger';
//
// // 基本使用
// Logger.info('服务启动成功', { port: 3000 });
// Logger.warn('连接超时，正在重试...');
// Logger.error('数据库连接失败', new Error('Connection timeout'));
// Logger.success('订单处理完成', { orderId: '12345' });
//
// // 配置日志器
// Logger.configure({ level: LogLevel.DEBUG, enableColors: false });
//
// // 使用上下文日志器
// const dbLogger = Logger.child('Database');
// dbLogger.info('连接已建立');
// dbLogger.error('查询失败', new Error('Syntax error'));