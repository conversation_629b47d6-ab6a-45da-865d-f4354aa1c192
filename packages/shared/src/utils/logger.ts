import winston from 'winston';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 日志配置接口
 */
export interface LoggerConfig {
    level?: string;
    enableFileLogging?: boolean;
    logDir?: string;
    enableColors?: boolean;
    dateFormat?: string;
}

/**
 * 基于 Winston 的增强日志工具类
 * 支持彩色输出、时间戳、错误堆栈跟踪、文件输出
 */
export class Logger {
    private static winstonLogger: winston.Logger;
    private static config: LoggerConfig = {
        level: 'info',
        enableFileLogging: true,
        logDir: 'logs',
        enableColors: true,
        dateFormat: 'YYYY-MM-DD HH:mm:ss'
    };

    /**
     * 初始化 Winston 日志器
     */
    private static initializeLogger(): void {
        if (this.winstonLogger) return;

        // 确保日志目录存在
        if (this.config.enableFileLogging && this.config.logDir) {
            if (!fs.existsSync(this.config.logDir)) {
                fs.mkdirSync(this.config.logDir, { recursive: true });
            }
        }

        // 自定义控制台格式 - 带日期前缀和错误堆栈
        const consoleFormat = winston.format.printf(({ level, message, timestamp, stack, ...meta }) => {
            // 处理错误堆栈
            let errorStack = '';
            if (stack) {
                errorStack = `\n${stack}`;
            }

            // 处理元数据
            const metaKeys = Object.keys(meta);
            let metaString = '';
            if (metaKeys.length > 0) {
                // 过滤掉 winston 内部字段
                const filteredMeta = Object.keys(meta)
                    .filter(key => !['level', 'message', 'timestamp', 'stack'].includes(key))
                    .reduce((obj, key) => {
                        obj[key] = meta[key];
                        return obj;
                    }, {} as any);

                if (Object.keys(filteredMeta).length > 0) {
                    metaString = ` ${JSON.stringify(filteredMeta, null, 2)}`;
                }
            }

            return `${timestamp} [${level.toUpperCase()}] ${message}${metaString}${errorStack}`;
        });

        // 创建传输器数组
        const transports: winston.transport[] = [
            // 控制台输出
            new winston.transports.Console({
                format: winston.format.combine(
                    winston.format.timestamp({ format: this.config.dateFormat }),
                    winston.format.errors({ stack: true }),
                    this.config.enableColors ? winston.format.colorize() : winston.format.uncolorize(),
                    consoleFormat
                )
            })
        ];

        // 添加文件输出
        if (this.config.enableFileLogging && this.config.logDir) {
            // 所有日志文件
            transports.push(new winston.transports.File({
                filename: path.join(this.config.logDir, 'combined.log'),
                format: winston.format.combine(
                    winston.format.timestamp({ format: this.config.dateFormat }),
                    winston.format.errors({ stack: true }),
                    winston.format.json()
                )
            }));

            // 错误日志文件
            transports.push(new winston.transports.File({
                filename: path.join(this.config.logDir, 'error.log'),
                level: 'error',
                format: winston.format.combine(
                    winston.format.timestamp({ format: this.config.dateFormat }),
                    winston.format.errors({ stack: true }),
                    winston.format.json()
                )
            }));
        }

        // 创建 Winston 日志器
        this.winstonLogger = winston.createLogger({
            level: this.config.level,
            format: winston.format.combine(
                winston.format.timestamp({ format: this.config.dateFormat }),
                winston.format.errors({ stack: true })
            ),
            transports
        });
    }

    /**
     * 配置日志器
     */
    static configure(config: Partial<LoggerConfig>): void {
        this.config = { ...this.config, ...config };
        // 重新初始化日志器
        this.winstonLogger = null as any;
        this.initializeLogger();
    }

    /**
     * 获取 Winston 日志器实例
     */
    private static getLogger(): winston.Logger {
        if (!this.winstonLogger) {
            this.initializeLogger();
        }
        return this.winstonLogger;
    }

    /**
     * 调试日志
     */
    static debug(message: string, meta?: any): void {
        this.getLogger().debug(message, meta);
    }

    /**
     * 信息日志
     */
    static info(message: string, meta?: any): void {
        this.getLogger().info(message, meta);
    }

    /**
     * 警告日志
     */
    static warn(message: string, meta?: any): void {
        this.getLogger().warn(message, meta);
    }

    /**
     * 错误日志 - 自动处理 Error 对象的堆栈跟踪
     */
    static error(message: string, error?: Error | any): void {
        if (error instanceof Error) {
            this.getLogger().error(message, { stack: error.stack, ...error });
        } else {
            this.getLogger().error(message, error);
        }
    }

    /**
     * 成功日志（特殊的 info 日志）
     */
    static success(message: string, meta?: any): void {
        this.getLogger().info(`✅ ${message}`, meta);
    }

    /**
     * 创建带上下文的日志器
     */
    static child(context: string) {
        const childLogger = this.getLogger().child({ context });

        return {
            debug: (message: string, meta?: any) => childLogger.debug(message, meta),
            info: (message: string, meta?: any) => childLogger.info(message, meta),
            warn: (message: string, meta?: any) => childLogger.warn(message, meta),
            error: (message: string, error?: Error | any) => {
                if (error instanceof Error) {
                    childLogger.error(message, { stack: error.stack, ...error });
                } else {
                    childLogger.error(message, error);
                }
            },
            success: (message: string, meta?: any) => childLogger.info(`✅ ${message}`, meta)
        };
    }

    /**
     * 设置日志级别
     */
    static setLevel(level: string): void {
        this.config.level = level;
        if (this.winstonLogger) {
            this.winstonLogger.level = level;
        }
    }

    /**
     * 启用/禁用文件日志
     */
    static setFileLogging(enabled: boolean, logDir?: string): void {
        this.config.enableFileLogging = enabled;
        if (logDir) {
            this.config.logDir = logDir;
        }
        // 重新初始化日志器
        this.winstonLogger = null as any;
        this.initializeLogger();
    }

    /**
     * 启用/禁用颜色输出
     */
    static setColors(enabled: boolean): void {
        this.config.enableColors = enabled;
        // 重新初始化日志器
        this.winstonLogger = null as any;
        this.initializeLogger();
    }

    /**
     * 获取底层的 Winston 日志器实例（用于高级用法）
     */
    static getWinstonLogger(): winston.Logger {
        return this.getLogger();
    }
}

// 使用示例：
// import { Logger } from './logger';
//
// // 基本使用
// Logger.info('服务启动成功', { port: 3000, env: 'development' });
// Logger.warn('内存使用率较高', { usage: '85%', threshold: '80%' });
// Logger.error('数据库连接失败', new Error('Connection timeout'));
// Logger.success('订单处理完成', { orderId: 'ORD-12345' });
//
// // 配置日志器
// Logger.configure({
//     level: 'debug',
//     enableFileLogging: true,
//     logDir: './logs',
//     enableColors: true
// });
//
// // 使用上下文日志器
// const dbLogger = Logger.child('Database');
// dbLogger.info('连接已建立');
// dbLogger.error('查询失败', new Error('Syntax error'));
//
// // 设置日志级别
// Logger.setLevel('warn'); // 只显示 warn 和 error 级别的日志

// 使用示例：
// import { Logger, LogLevel } from './logger';
//
// // 基本使用
// Logger.info('服务启动成功', { port: 3000 });
// Logger.warn('连接超时，正在重试...');
// Logger.error('数据库连接失败', new Error('Connection timeout'));
// Logger.success('订单处理完成', { orderId: '12345' });
//
// // 配置日志器
// Logger.configure({ level: LogLevel.DEBUG, enableColors: false });
//
// // 使用上下文日志器
// const dbLogger = Logger.child('Database');
// dbLogger.info('连接已建立');
// dbLogger.error('查询失败', new Error('Syntax error'));