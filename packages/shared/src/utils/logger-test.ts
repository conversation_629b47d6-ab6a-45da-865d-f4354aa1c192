import { Logger, LogLevel } from './logger';

/**
 * 日志工具类测试和演示
 */
function testLogger() {
    console.log('=== 日志工具类测试 ===\n');

    // 1. 基本日志测试
    console.log('1. 基本日志测试:');
    Logger.debug('这是调试信息', { userId: 123, action: 'login' });
    Logger.info('服务启动成功', { port: 3000, env: 'development' });
    Logger.warn('内存使用率较高', { usage: '85%', threshold: '80%' });
    Logger.error('数据库连接失败', new Error('Connection timeout after 5000ms'));
    Logger.success('订单处理完成', { orderId: 'ORD-12345', amount: 99.99 });

    console.log('\n2. 错误堆栈跟踪测试:');
    try {
        // 模拟一个错误
        throw new Error('模拟的业务逻辑错误');
    } catch (error) {
        Logger.error('捕获到异常', error);
    }

    console.log('\n3. 上下文日志器测试:');
    const dbLogger = Logger.child('Database');
    const redisLogger = Logger.child('Redis');
    const apiLogger = Logger.child('API');

    dbLogger.info('数据库连接已建立');
    dbLogger.warn('查询执行时间较长', { query: 'SELECT * FROM users', duration: '2.5s' });
    
    redisLogger.info('Redis 连接成功');
    redisLogger.error('Redis 连接断开', new Error('Connection lost'));
    
    apiLogger.info('API 请求处理', { method: 'POST', path: '/api/orders', status: 200 });

    console.log('\n4. 日志级别测试:');
    console.log('设置日志级别为 WARN，DEBUG 和 INFO 将不显示:');
    Logger.setLevel(LogLevel.WARN);
    Logger.debug('这条调试信息不会显示');
    Logger.info('这条信息日志不会显示');
    Logger.warn('这条警告会显示');
    Logger.error('这条错误会显示');

    console.log('\n5. 禁用颜色测试:');
    Logger.setColors(false);
    Logger.setLevel(LogLevel.INFO); // 重置级别
    Logger.info('无颜色的日志信息');
    Logger.error('无颜色的错误信息', new Error('测试错误'));

    console.log('\n6. 禁用时间戳测试:');
    Logger.setTimestamp(false);
    Logger.info('无时间戳的日志信息');

    // 恢复默认设置
    Logger.setColors(true);
    Logger.setTimestamp(true);
    Logger.setLevel(LogLevel.INFO);

    console.log('\n7. 复杂对象日志测试:');
    const complexObject = {
        user: {
            id: 123,
            name: 'John Doe',
            email: '<EMAIL>'
        },
        order: {
            id: 'ORD-456',
            items: [
                { name: 'Product A', price: 29.99, quantity: 2 },
                { name: 'Product B', price: 19.99, quantity: 1 }
            ],
            total: 79.97
        },
        timestamp: new Date().toISOString()
    };

    Logger.info('处理复杂订单数据', complexObject);

    console.log('\n=== 测试完成 ===');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    testLogger();
}

export { testLogger };
