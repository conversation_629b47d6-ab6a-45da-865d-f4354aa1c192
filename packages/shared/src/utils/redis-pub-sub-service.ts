import Redis, {RedisOptions} from 'ioredis';
import { RedisConfig, RedisServiceConfig } from "../types/types";

/**
 * Redis 发布订阅服务 - 支持配置传入的单例模式
 */
export class RedisPubSubService {
    private static instance: RedisPubSubService;
    private client: Redis;
    private subscriber: Redis;
    private config: RedisServiceConfig;

    private constructor(config: RedisServiceConfig = {}) {
        this.config = config;

        // 默认配置
        const defaultRedisConfig: RedisConfig = {
            host: "127.0.0.1",
            port: 6379,
            password: undefined,
            db: 0,
            retryStrategy: (times) => Math.min(times * 50, 2000),
            maxRetriesPerRequest: null,
            enableReadyCheck: true,
            reconnectOnError: (err) => {
                const targetError = 'READONLY';
                return err.message.includes(targetError);
            },
        };

        // 合并配置
        const redisConfig = { ...defaultRedisConfig, ...config.redis };

        if (config.enableLogging) {
            console.log(`init RedisPubSubService, isDev: ${config.isDev}, host: ${redisConfig.host}, port: ${redisConfig.port}, password: ${redisConfig.password ? '***' : 'none'}`);
        }

        const redisOptions: RedisOptions = {
            host: redisConfig.host,
            port: redisConfig.port,
            password: redisConfig.password,
            db: redisConfig.db,
            retryStrategy: redisConfig.retryStrategy,
            maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
            enableReadyCheck: redisConfig.enableReadyCheck,
            reconnectOnError: redisConfig.reconnectOnError,
        };

        this.client = new Redis(redisOptions);
        this.subscriber = new Redis(redisOptions);

        this.setupEventListeners();
    }

    private setupEventListeners() {
        if (!this.config.enableLogging) return;

        this.client.on('error', (err) => console.error('Redis error:', err));
        this.subscriber.on('error', (err) => console.error('Subscriber error:', err));

        this.client.on('connect', () => console.info('Redis client connected'));
        this.subscriber.on('connect', () => console.info('Redis subscriber connected'));

        this.client.on('ready', () => console.info('Redis client ready'));
        this.subscriber.on('ready', () => console.info('Redis subscriber ready'));
    }

    /**
     * 获取单例实例
     * @param config Redis 服务配置
     */
    public static getSingleInstance(config: RedisServiceConfig = {}): RedisPubSubService {
        if (!RedisPubSubService.instance) {
            if (config.enableLogging) {
                console.log(`RedisPubSubService Init Success!`);
            }
            RedisPubSubService.instance = new RedisPubSubService(config);
        }
        return RedisPubSubService.instance;
    }

    /**
     * 创建新实例（非单例模式）
     * @param config Redis 服务配置
     */
    public static getInstance(config: RedisServiceConfig = {}): RedisPubSubService {
        return new RedisPubSubService(config);
    }

    /**
     * 重置单例实例（用于测试或重新配置）
     */
    public static resetInstance(): void {
        if (RedisPubSubService.instance) {
            RedisPubSubService.instance.close();
            RedisPubSubService.instance = null as any;
        }
    }

    // 发布消息
    public async publish<T>(channel: string, message: T): Promise<void> {
        await this.client.publish(channel, JSON.stringify(message));
    }

    // 订阅消息
    public async subscribe<T>(channel: string, callback: (message: T) => void): Promise<void> {
        await this.subscriber.subscribe(channel);
        this.subscriber.on('message', (ch, message) => {
            if (ch === channel) {
                try {
                    const parsedMessage: T = JSON.parse(message);
                    callback(parsedMessage);
                } catch (err) {
                    console.error('Error parsing message:', err);
                }
            }
        });
    }

    // 关闭连接
    public async close(): Promise<void> {
        await this.client.quit();
        await this.subscriber.quit();
        if (this.config.enableLogging) {
            console.log('Redis clients closed');
        }
    }

    /**
     * 获取客户端实例（用于高级操作）
     */
    public getClient(): Redis {
        return this.client;
    }

    /**
     * 获取订阅客户端实例（用于高级操作）
     */
    public getSubscriber(): Redis {
        return this.subscriber;
    }
}

/**
 * 获取 Redis 发布订阅服务实例（单例模式）
 * @param config Redis 服务配置
 */
export const getSingleRedisPubSubService = (config: RedisServiceConfig = {}): RedisPubSubService =>
    RedisPubSubService.getSingleInstance(config);

/**
 * 创建新的 Redis 发布订阅服务实例（非单例模式）
 * @param config Redis 服务配置
 */
export const getRedisPubSubService = (config: RedisServiceConfig = {}): RedisPubSubService =>
    RedisPubSubService.getInstance(config);