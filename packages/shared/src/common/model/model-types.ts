export interface SwapModel {
    chainType: string;
    mint: string;
    pool: string;
    symbol: string;
    isBuy: boolean;
    amount: number;
    usdPrice: number;
    swapUsdPrice: number;
    marketCap: number;
    anchorUsdLiquidity: number;
    tokenUsdLiquidity: number;
    totalUsdLiquidity: number;
    currentAnchorUsdPrice: number;
    user: string;
    blockTime: number;
    block: number;
    signature: string;
    diffTime: number;
    dexType: string;
}

export interface SwapEvent {
    signatures: string;
    slot: number;
    user: string;
    timestamp: number;
    rayAmmV4PoolSwaps: PoolSwapInfo[];
    rayClmmPoolSwaps: PoolSwapInfo[];
    rayCpmmPoolSwaps: PoolSwapInfo[];
    orcaWhirlPoolSwaps: PoolSwapInfo[];
    meteoraDlmmPoolSwaps: PoolSwapInfo[];
    meteoraPoolsSwaps?: PoolSwapInfo[];
    meteoraDammV2Swaps?: PoolSwapInfo[];
    pumpAmmPoolSwaps?: PoolSwapInfo[];
    index?: number;
}

export interface PoolMarketInfo {
    anchorSymbol: string; // 锚定币的symbol
    anchorMint: string; // 锚定币
    poolAnchorBalance: number;      // 池子中的锚定币余额
    poolTokenBalance: number;    // 池子中的token余额
    tokenDecimals: number;       // token的小数位数
    anchorTokenPrice: number;    // 每个token对应的锚定币价值
    usdTokenPrice: number;    // 每个token对应的USD价值
    totalAnchorValue: number | null;       // 总市值(以锚定币计)
    totalUsdValue: number | null;       // 总市值(usd计)
    currentAnchorUsdPrice: number; // 当前锚定币价格
    tokenSymbol?: string;
    tokenName?: string;
    tokenUri?: string
}


export interface PoolSwapInfo {
    tokenInMint: string;
    tokenOutMint: string;
    tokenInAmount: number;
    tokenOutAmount: number;
    tokenInDecimals: number;
    tokenOutDecimals: number;
    poolAddress: string | null;
    marketInfo: PoolMarketInfo | null;
    type: string
}



// 定义消息数据接口
export interface MessageData {
    topic: string;
    message: string;
    [key: string]: any; // 允许其他可能的字段
}