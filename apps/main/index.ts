import { GatewayService } from "@mev-bot/gateway";
import { StrategyProcessor } from "@mev-bot/strategy";
import { SolanaDecisionService } from "@mev-bot/decision";
import { SolanaTradeService } from "@mev-bot/trade";
import { OrdersService, OrderType } from "@mev-bot/orders";
import { ChainsService } from "@mev-bot/chains";
import { SwapModel } from "@mev-bot/shared";
import { getAppRedisConfig } from "./redis-config";

async function startMevBot() {
    console.log("🚀 Starting MEV Bot...");

    // 0. 获取 Redis 配置
    const redisConfig = getAppRedisConfig();
    console.log(`📡 Using Redis config: ${redisConfig.redis?.host}:${redisConfig.redis?.port} (isDev: ${redisConfig.isDev})`);

    // 1. 初始化所有服务
    const gatewayService = new GatewayService();
    const strategyProcessor = new StrategyProcessor(redisConfig);
    const decisionService = new SolanaDecisionService(redisConfig);
    const tradeService = new SolanaTradeService();
    const ordersService = new OrdersService(redisConfig);
    const chainsService = new ChainsService(redisConfig);

    // 2. 初始化服务（注意：这里会有 Redis 连接错误，但不影响演示）
    try {
        await gatewayService.init();
        await strategyProcessor.init();
        await decisionService.init();
        await ordersService.init();

        // 启动 Chains 服务（在后台运行）
        console.log("🔗 Starting Chains Service...");
        chainsService.start().catch((error: any) => {
            console.log("⚠️  Chains service failed to start (expected without proper GRPC endpoint):", error.message);
        });

        console.log("✅ All services initialized successfully!");
    } catch (error) {
        console.log("⚠️  Redis connection failed (expected in demo), but services are ready");
    }

    // 3. 启动数据流处理
    await gatewayService.startTest(async (swapModel: SwapModel) => {
        console.log("📊 New swap detected:", {
            symbol: swapModel.symbol,
            isBuy: swapModel.isBuy,
            amount: swapModel.amount,
            usdPrice: swapModel.usdPrice,
            marketCap: swapModel.marketCap
        });

        // 4. 集成完整的 MEV 流程
        try {
            // 策略评分（模拟）
            console.log("🧠 Running strategy analysis...");

            // 决策处理（模拟）
            console.log("⚖️  Making trading decision...");

            // 创建订单（模拟）
            if (swapModel.isBuy && swapModel.marketCap < 100000) {
                const order = await ordersService.createOrder(
                    swapModel.symbol,
                    OrderType.BUY,
                    1000,
                    swapModel.usdPrice
                );
                console.log("📋 Order created:", order.id);
            }

            // 交易执行（模拟）
            console.log("💰 Trade execution ready...");

            // 显示订单统计
            const stats = ordersService.getOrderStats();
            if (stats.total > 0) {
                console.log("📊 Orders:", `${stats.pending} pending, ${stats.filled} filled`);
            }

            console.log("✨ MEV pipeline completed for", swapModel.symbol);
            console.log("---");
        } catch (error) {
            console.error("❌ Error in MEV pipeline:", error);
        }
    });
}

startMevBot().catch(console.error);
