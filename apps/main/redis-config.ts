import { 
    RedisServiceConfig, 
    createRedisConfigFromEnv,
    createDevRedisConfig,
    createProdRedisConfig,
    createTestRedisConfig 
} from '@mev-bot/shared';

/**
 * 主应用的 Redis 配置管理
 */
export class AppRedisConfig {
    /**
     * 获取当前环境的 Redis 配置
     */
    public static getConfig(): RedisServiceConfig {
        const env = process.env.NODE_ENV || 'development';
        
        switch (env) {
            case 'production':
                return this.getProductionConfig();
            case 'test':
                return createTestRedisConfig();
            case 'development':
            default:
                return this.getDevelopmentConfig();
        }
    }

    /**
     * 获取开发环境配置
     */
    private static getDevelopmentConfig(): RedisServiceConfig {
        // 优先从环境变量读取，如果没有则使用默认开发配置
        if (process.env.REDIS_HOST) {
            return createRedisConfigFromEnv();
        }
        
        return createDevRedisConfig();
    }

    /**
     * 获取生产环境配置
     */
    private static getProductionConfig(): RedisServiceConfig {
        // 生产环境必须从环境变量读取
        const host = process.env.REDIS_HOST;
        const port = parseInt(process.env.REDIS_PORT || '6379');
        const password = process.env.REDIS_PASSWORD;
        const db = parseInt(process.env.REDIS_DB || '0');

        if (!host) {
            throw new Error('REDIS_HOST environment variable is required in production');
        }

        return createProdRedisConfig(host, port, password, db);
    }

    /**
     * 获取自定义配置（用于特殊需求）
     */
    public static getCustomConfig(
        host: string,
        port: number,
        password?: string,
        db: number = 0,
        isDev: boolean = false
    ): RedisServiceConfig {
        return {
            redis: {
                host,
                port,
                password,
                db,
                retryStrategy: (times) => Math.min(times * (isDev ? 50 : 100), isDev ? 2000 : 5000),
                maxRetriesPerRequest: isDev ? null : 3,
                enableReadyCheck: true,
                reconnectOnError: (err) => {
                    const targetErrors = ['READONLY', 'ECONNRESET', 'ENOTFOUND'];
                    return targetErrors.some(error => err.message.includes(error));
                },
            },
            isDev,
            enableLogging: isDev,
        };
    }
}

// 导出便捷函数
export const getAppRedisConfig = () => AppRedisConfig.getConfig();
