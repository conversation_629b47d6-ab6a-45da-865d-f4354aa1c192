{"name": "@mev-bot/main", "version": "1.0.0", "description": "MEV Bot Main Application", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node index.ts", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist", "watch": "tsc --watch"}, "dependencies": {"@mev-bot/shared": "*", "@mev-bot/gateway": "*", "@mev-bot/strategy": "*", "@mev-bot/decision": "*", "@mev-bot/trade": "*", "@mev-bot/orders": "*", "@mev-bot/chains": "*"}, "devDependencies": {"@types/node": "^18.19.75", "rimraf": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}