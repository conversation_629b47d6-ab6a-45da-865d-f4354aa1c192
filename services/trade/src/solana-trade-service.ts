/**
 * solana交易服务
 */
import {
    AddressLookupTableAccount, BlockhashWithExpiryBlockHeight,
    Connection,
    PublicKey,
    SystemProgram, TransactionInstruction,
    TransactionMessage,
    VersionedTransaction
} from "@solana/web3.js";
import {JITO_TIP_ACCOUNTS, MEV_JITO_RPCS, SOL_MINT, solana_rpc} from "@mev-bot/shared";


export class SolanaTradeService {

    // todo 没想好每种池子的dex交易，这里暂时用jup api来吧 可能会限流 这里暂时先写一下大概流程
    constructor() {

    }

    /**
     * 涡轮模式发送交易
     * @param amount
     * @param poolAddress
     * @param tokenAddress
     * @param slippage
     * @param isBuy
     * @param publicKeyStr
     * @param totalFee
     */
    public async turbineBuildTx(amount: number, poolAddress: string, tokenAddress: string, slippage: number, isBuy: boolean, publicKeyStr: string, totalFee: number) {
        try {

            const connection = new Connection(solana_rpc, 'confirmed');
            const userPublicKey = new PublicKey(publicKeyStr);
            let allInstructions: TransactionInstruction[] = [];


            let actualTotalFee = totalFee;

            // 随机jito小费账户
            const randomTipAccount = new PublicKey(JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)]);
            const jitoTipAccount = new PublicKey(randomTipAccount);

            // 计算jito小费 (30%)
            const jitoTipAmount = Math.floor(totalFee * 0.3);
            // 总费用扣除这一部分
            actualTotalFee = actualTotalFee - jitoTipAmount;

            const jitoTipInstruction = SystemProgram.transfer({
                fromPubkey: userPublicKey,
                toPubkey: jitoTipAccount,
                lamports: jitoTipAmount
            });
            allInstructions.push(jitoTipInstruction);

            let inputMint = isBuy ? SOL_MINT : tokenAddress;
            let outputMint = isBuy ? tokenAddress : SOL_MINT;
            let quoteReq = {
                amount: amount.toString(),
                slippageBps: slippage,
                swapMode: 'ExactIn',
                onlyDirectRoutes: false,
                maxAccounts: 64,
                minimizeSlippage: false,
                inputMint: inputMint,
                outputMint: outputMint
            };

            const quoteResponse = await this.getQuote(quoteReq);

            const swapRequest = {
                quoteResponse: quoteResponse,
                userPublicKey: userPublicKey.toString(),
                prioritizationFeeLamports: {
                    priorityLevelWithMaxLamports: {
                        maxLamports: actualTotalFee,
                        priorityLevel: "veryHigh"
                    }
                }
            };

            // 构建交易指令
            const swapInstructionsResponse = await this.buildSwapInstructions(swapRequest);

            let blockhashAndLookupTables = await this.fetchBlockhashAndLookupTables(connection, swapInstructionsResponse);


            // 1. 添加计算预算指令
            if (Array.isArray(swapInstructionsResponse.computeBudgetInstructions)) {
                for (const instruction of swapInstructionsResponse.computeBudgetInstructions) {
                    allInstructions.push(this.processInstruction(instruction));
                }
            }

            // 2. 添加设置指令
            if (Array.isArray(swapInstructionsResponse.setupInstructions)) {
                for (const instruction of swapInstructionsResponse.setupInstructions) {
                    allInstructions.push(this.processInstruction(instruction));
                }
            }

            // 3. Token ledger指令（如果有）
            if (swapInstructionsResponse.tokenLedgerInstruction) {
                allInstructions.push(this.processInstruction(swapInstructionsResponse.tokenLedgerInstruction));
            }

            // 4. 主swap指令
            if (swapInstructionsResponse.swapInstruction) {
                allInstructions.push(this.processInstruction(swapInstructionsResponse.swapInstruction));
            }

            // 5. 清理指令（如果有）
            if (swapInstructionsResponse.cleanupInstruction) {
                allInstructions.push(this.processInstruction(swapInstructionsResponse.cleanupInstruction));
            }

            // 构建交易消息
            const messageV0 = new TransactionMessage({
                payerKey: userPublicKey,
                recentBlockhash: blockhashAndLookupTables.blockhash,
                instructions: allInstructions
            }).compileToV0Message(blockhashAndLookupTables.addressLookupTableAccounts);

            // 创建并序列化交易
            return new VersionedTransaction(messageV0);
        } catch (error) {
            console.error('交易失败:', error);
            console.error('错误详情:', error instanceof Error ? error.stack : error);
            throw error;
        }
    }


    /**
     * 涡轮模式发送交易
     * @param transactionData - 交易数据的 base64 编码字符串
     * @returns 成功交易的签名字符串，或 null 如果没有成功提交
     */
    private async turbineSendTx(transactionData: any): Promise<any> {
        const jitoParam: any = {
            jsonrpc: '2.0',
            id: 1,
            method: 'sendTransaction',
            params: [transactionData, {encoding: 'base64', maxRetries: 5}],
        };

        const solParam: any = {
            jsonrpc: '2.0',
            id: 1,
            method: 'sendTransaction',
            params: [
                transactionData,
                {
                    encoding: 'base64',
                    maxRetries: 5,
                    preflightCommitment: 'confirmed',
                    skipPreflight: true,
                },
            ],
        };

        // 构造请求数组
        const requests = [
            // Jito 节点请求
            ...MEV_JITO_RPCS.map((rpc: any) => ({
                url: `${rpc}/api/v1/transactions`,
                param: jitoParam,
                rpc,
            })),
            {
                url: solana_rpc,
                param: solParam,
                rpc: solana_rpc,
            },
            {
                url: solana_rpc,
                param: solParam,
                rpc: solana_rpc,
            },
        ].map(({url, param, rpc}: any) =>
            fetch(url, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(param),
            })
                .then(async (response: Response) => {
                    if (!response.ok) {
                        throw new Error(`HTTP error ${response.status} from ${rpc}`);
                    }
                    const data = await response.json();
                    return {rpc, data};
                })
        );

        // 并发发送并等待第一个成功响应
        const firstSuccess = await Promise.race(
            requests.map(async (request: any) => {
                try {
                    const {rpc, data} = await request;
                    if (data.result) {
                        return data.result; // 返回第一个成功的 tx 签名
                    }
                    console.warn(`No result from RPC ${rpc}`);
                    return null;
                } catch (error) {
                    console.error(`Error from RPC :`, error);
                    return null;
                }
            })
        );

        if (firstSuccess) {
            return firstSuccess; // 返回成功的签名
        }
        console.log('No successful transaction submissions.');
        return null;
    }

    private processInstruction(instruction: any): TransactionInstruction {
        if (!instruction || !instruction.programId) {
            console.error("Missing required instruction fields:", instruction);
            throw new Error("Invalid instruction format");
        }

        let result: TransactionInstruction = {
            programId: new PublicKey(instruction.programId),
            keys: (instruction.accounts || []).map((account: any) => ({
                pubkey: new PublicKey(account.pubkey),
                isSigner: !!account.isSigner,
                isWritable: !!account.isWritable
            })),
            data: Buffer.from(instruction.data || '', 'base64')
        } as TransactionInstruction

        return result;
    }

    private async fetchBlockhashAndLookupTables(
        connection: Connection,
        swapInstructionsResponse: any
    ): Promise<{
        blockhash: string;
        addressLookupTableAccounts: AddressLookupTableAccount[];
    }> {
        // 先获取 blockhash
        const {blockhash} = await connection.getLatestBlockhash('confirmed');

        // 再处理地址查找表
        let addressLookupTableAccounts: AddressLookupTableAccount[] = [];

        if (swapInstructionsResponse.addressLookupTableAddresses &&
            swapInstructionsResponse.addressLookupTableAddresses.length > 0) {
            // 串行处理每个地址查找表
            for (const address of swapInstructionsResponse.addressLookupTableAddresses) {
                const account = await connection
                    .getAddressLookupTable(new PublicKey(address))
                    .then((res) => res.value);

                if (!account) {
                    throw new Error(`查找表 ${address} 未找到`);
                }

                addressLookupTableAccounts.push(account);
            }
        }

        return {blockhash, addressLookupTableAccounts};
    }


    private async getQuote(quoteReq: any): Promise<any> {
        // 构建 URL 并附加查询参数
        const url = new URL('https://api.jup.ag/swap/v1/quote');
        Object.keys(quoteReq).forEach(key => url.searchParams.append(key, quoteReq[key]));

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`获取报价失败: ${response.statusText}`);
        }

        return response.json();
    }


    async buildSwapInstructions(swapRequest: any): Promise<any> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json' // 可以添加apiKey, 免费版本的就不用
        };

        const response = await fetch('https://api.jup.ag/swap/v1/swap-instructions', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(swapRequest) // fetch 需要手动序列化 body
        });

        // 检查响应是否成功
        if (!response.ok) {
            throw new Error(`构建jup swapTx失败: ${response.statusText}`);
        }

        const data = await response.json();

        if (!data) {
            throw new Error('构建jup swapTx失败...');
        }

        return data;
    }

}

// 如果直接运行此文件，启动服务
if (require.main === module) {
    const tradeService = new SolanaTradeService();
    console.log('🚀 Trade Service started successfully!');
    console.log('💰 Trade service is ready to process trading requests...');

    // 演示交易构建（不实际发送）
    setTimeout(async () => {
        try {
            console.log('📝 Demo: Building a sample transaction...');
            // 这里可以添加一些演示代码，比如构建一个示例交易
            console.log('✅ Trade service is working properly!');
        } catch (error) {
            console.error('❌ Trade service demo failed:', error);
        }
    }, 2000);
}