{"name": "@mev-bot/chains", "version": "1.0.0", "description": "Chain data collection service for MEV Bot", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "watch": "ts-node-dev --respawn src/index.ts"}, "keywords": ["solana", "blockchain", "dex", "mev", "chains"], "author": "", "license": "ISC", "devDependencies": {"@types/lodash": "^4.17.4", "@types/node": "^20.10.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.4.5"}, "dependencies": {"@mev-bot/shared": "*", "@coral-xyz/anchor": "^0.30.1", "@shyft-to/solana-transaction-parser": "^2.0.0", "@solana/web3.js": "^1.93.0", "@triton-one/yellowstone-grpc": "^4.0.0", "dotenv": "^16.4.7", "lodash": "^4.17.21", "redis": "^4.6.0"}}