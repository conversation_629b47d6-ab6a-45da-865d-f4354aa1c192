import { RedisService } from '../../../utils/RedisService';
import { PumpFunAmmTransactionListener } from './listener/PumpFunAmmTransactionListener';

export class PumpFunAMMService {
    private redisService: RedisService;
    private transactionListener: PumpFunAmmTransactionListener;
    private isRunning: boolean = false;

    constructor(redisService: RedisService) {
        this.redisService = redisService;
        this.transactionListener = new PumpFunAmmTransactionListener(redisService);
    }

    async start(): Promise<void> {
        if (this.isRunning) {
            console.log('PumpFunAMMService is already running');
            return;
        }

        try {
            console.log('🚀 Starting PumpFun AMM Service...');

            // 启动交易监听器
            this.transactionListener.start();
            console.log('✅ PumpFun AMM Transaction Listener started');

            this.isRunning = true;
            console.log('🎉 PumpFun AMM Service started successfully');

        } catch (error) {
            console.error('❌ Failed to start PumpFun AMM Service:', error);
            throw error;
        }
    }

    async stop(): Promise<void> {
        if (!this.isRunning) {
            return;
        }

        console.log('🛑 Stopping PumpFun AMM Service...');
        
        try {
            await this.transactionListener.stop();
            
            this.isRunning = false;
            console.log('✅ PumpFun AMM Service stopped');
        } catch (error) {
            console.error('❌ Error stopping PumpFun AMM Service:', error);
            throw error;
        }
    }

    isServiceRunning(): boolean {
        return this.isRunning;
    }

    getTransactionListener(): PumpFunAmmTransactionListener {
        return this.transactionListener;
    }
}
