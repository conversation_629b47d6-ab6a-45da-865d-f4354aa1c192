import "dotenv/config";
import Client, {
    CommitmentLevel,
    SubscribeRequestAccountsDataSlice,
    SubscribeRequestFilterAccounts,
    SubscribeRequestFilterBlocks,
    SubscribeRequestFilterBlocksMeta,
    SubscribeRequestFilterEntry,
    SubscribeRequestFilterSlots,
    SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { PublicKey, VersionedTransactionResponse } from "@solana/web3.js";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser, ParsedInstruction } from "@shyft-to/solana-transaction-parser";
import { TransactionFormatter } from "../utils/TransactionFormatter";
import { SolanaEventParser } from "../utils/EventParser";
import { bnLayoutFormatter } from "../utils/BnLayoutFormatter";
import pumpFunAmmIdl from "../idls/pump_amm_0.1.0.json";
import { SubscribeRequestPing } from "@triton-one/yellowstone-grpc/dist/types/grpc/geyser";
import { BlockTransactionFormatter } from "../utils/BlockTransactionFormatter";
import { RedisService } from "../../../../utils/RedisService";

interface SubscribeRequest {
    accounts: { [key: string]: SubscribeRequestFilterAccounts };
    slots: { [key: string]: SubscribeRequestFilterSlots };
    transactions: { [key: string]: SubscribeRequestFilterTransactions };
    transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
    blocks: { [key: string]: SubscribeRequestFilterBlocks };
    blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
    entry: { [key: string]: SubscribeRequestFilterEntry };
    commitment?: CommitmentLevel | undefined;
    accountsDataSlice: SubscribeRequestAccountsDataSlice[];
    ping?: SubscribeRequestPing | undefined;
}

export interface PumpFunTransaction {
    signature: string;
    quoteMint: string;
    baseMint: string;
    events: any[];
    slot: number;
    blockHeight: number;
    blockTime: number;
    blockHash: string;
}

export class PumpFunAmmTransactionListener {
    private client: Client;
    private req: SubscribeRequest;
    private txnFormatter: TransactionFormatter;
    private blockTxnFormatter: BlockTransactionFormatter;
    private pumpFunIxParser: SolanaParser;
    private pumpFunEventParser: SolanaEventParser;
    private pumpFunAmmProgramId: PublicKey;
    private redisService: RedisService;
    private isRunning: boolean = false;
    private readonly BASE_MINT_INDEX = 3;
    private readonly QUOTE_MINT_INDEX = 4;
    private readonly REDIS_CHANNEL = 'pumpfun_amm_transactions';

    constructor(redisService: RedisService) {
        this.redisService = redisService;
        
        let endpoint = process.env.ENDPOINT || process.env.GRPC_ENDPOINT;
        if (!endpoint) {
            console.warn('⚠️  No GRPC endpoint configured, using demo mode');
            endpoint = 'demo://localhost:9090'; // 演示模式
        }
        
        this.client = new Client(endpoint, undefined, undefined);
        this.txnFormatter = new TransactionFormatter();
        this.blockTxnFormatter = new BlockTransactionFormatter();
        this.pumpFunAmmProgramId = new PublicKey(
            "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"
        );
        this.pumpFunIxParser = new SolanaParser([]);
        this.pumpFunIxParser.addParserFromIdl(
            this.pumpFunAmmProgramId.toBase58(),
            pumpFunAmmIdl as Idl
        );
        this.pumpFunEventParser = new SolanaEventParser([], console);
        this.pumpFunEventParser.addParserFromIdl(
            this.pumpFunAmmProgramId.toBase58(),
            pumpFunAmmIdl as Idl
        );

        this.req = {
            accounts: {},
            slots: {},
            transactions: {},
            transactionsStatus: {},
            entry: {},
            blocks: {
                pumpFunAmm: {
                    accountInclude: [],
                    includeTransactions: true,
                    includeAccounts: false,
                    includeEntries: false
                }
            },
            blocksMeta: {},
            accountsDataSlice: [],
            ping: undefined,
            commitment: CommitmentLevel.PROCESSED,
        };
    }

    private decodePumpFunAmmTxn(tx: VersionedTransactionResponse) {
        if (!tx.meta) {
            return;
        }

        if (tx.meta?.err) return;

        const paredIxs = this.pumpFunIxParser.parseTransactionData(
            tx.transaction.message,
            tx.meta.loadedAddresses
        );

        const pumpFunIxs = paredIxs.filter((ix) =>
            ix.programId.equals(this.pumpFunAmmProgramId)
        );

        if (pumpFunIxs.length === 0) return;
        const events = this.pumpFunEventParser.parseEvent(tx);
        const result = { instructions: pumpFunIxs, events };
        bnLayoutFormatter(result);
        return result;
    }

    private async publishTransaction(transaction: PumpFunTransaction): Promise<void> {
        try {
            await this.redisService.publish(this.REDIS_CHANNEL, transaction);
            console.log(`📤 Published PumpFun transaction: ${transaction.signature}`);
        } catch (error) {
            console.error('Failed to publish transaction:', error);
        }
    }

    private async handleStream(): Promise<void> {
        // 检查是否为演示模式
        const endpoint = process.env.ENDPOINT || process.env.GRPC_ENDPOINT;
        if (!endpoint || endpoint.startsWith('demo://')) {
            console.log('📺 Running in demo mode - simulating PumpFun AMM transactions...');
            await this.simulateDemoTransactions();
            return;
        }

        const stream = await this.client.subscribe();

        const streamClosed = new Promise<void>((resolve, reject) => {
            stream.on("error", (error) => {
                console.log("ERROR", error);
                reject(error);
                stream.end();
            });
            stream.on("end", () => resolve());
            stream.on("close", () => resolve());
        });

        stream.on("data", async (data) => {
            if (data?.block) {
                let grpcBlock = data?.block;
                let slot = grpcBlock.slot;
                let blockHeight = grpcBlock.blockHeight.blockHeight;
                let blockTime = grpcBlock.blockTime.timestamp;
                let blockHash = grpcBlock.blockhash;
                let transactions = grpcBlock.transactions;
                
                if (transactions && transactions.length >= 0) {
                    let resultList = transactions
                        .map((transaction: any) => {
                            let txn = this.blockTxnFormatter.formTransactionFromJson(
                                transaction,
                                Number(blockTime)
                            );

                            let decodePumpFunAmmTxn = this.decodePumpFunAmmTxn(txn);
                            if (!decodePumpFunAmmTxn) return null;

                            // 直接访问第一个指令，使用 any 类型避免类型检查
                            const firstInstruction: any = decodePumpFunAmmTxn.instructions[0];
                            let result: PumpFunTransaction = {
                                signature: txn.transaction.signatures[0],
                                quoteMint: firstInstruction?.accounts?.[this.QUOTE_MINT_INDEX].pubkey,
                                baseMint: firstInstruction?.accounts?.[this.BASE_MINT_INDEX].pubkey,
                                events: decodePumpFunAmmTxn.events,
                                slot: Number(slot),
                                blockHeight: Number(blockHeight),
                                blockTime: Number(blockTime),
                                blockHash: blockHash
                            };
                            return result;
                        })
                        .filter((result: any) => result !== null);

                    console.log(`📊 Slot: ${slot}, BlockHeight: ${blockHeight}, BlockTime: ${blockTime}, Transactions: ${resultList.length}`);
                    
                    // 发布每个交易到 Redis
                    for (const transaction of resultList) {
                        if (transaction) {
                            await this.publishTransaction(transaction);
                        }
                    }
                }
            }
        });

        await new Promise<void>((resolve, reject) => {
            stream.write(this.req, (err: any) => {
                if (err === null || err === undefined) {
                    resolve();
                } else {
                    reject(err);
                }
            });
        }).catch((reason) => {
            console.error(reason);
            throw reason;
        });

        await streamClosed;
    }

    public start(): void {
        if (this.isRunning) {
            console.log('PumpFun AMM Transaction Listener is already running');
            return;
        }

        this.isRunning = true;
        console.log('🚀 Starting PumpFun AMM Transaction Listener...');
        
        // 在后台运行监听器
        this.subscribe().catch(error => {
            console.error('PumpFun AMM Transaction Listener error:', error);
            this.isRunning = false;
        });
    }

    public async stop(): Promise<void> {
        if (!this.isRunning) {
            return;
        }

        console.log('🛑 Stopping PumpFun AMM Transaction Listener...');
        this.isRunning = false;
        
        try {
            // 这里可以添加清理逻辑
            console.log('✅ PumpFun AMM Transaction Listener stopped');
        } catch (error) {
            console.error('❌ Error stopping PumpFun AMM Transaction Listener:', error);
            throw error;
        }
    }

    private async subscribe(): Promise<void> {
        while (this.isRunning) {
            try {
                await this.handleStream();
            } catch (error) {
                if (this.isRunning) {
                    console.error("Stream error, restarting in 1 second...", error);
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                }
            }
        }
    }

    public isListenerRunning(): boolean {
        return this.isRunning;
    }

    private async simulateDemoTransactions(): Promise<void> {
        console.log('🎭 Starting demo transaction simulation...');

        while (this.isRunning) {
            try {
                // 模拟一个 PumpFun AMM 交易
                const demoTransaction: PumpFunTransaction = {
                    signature: `demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    quoteMint: 'So11111111111111111111111111111111111111112', // SOL
                    baseMint: `demo_${Math.random().toString(36).substr(2, 9)}`, // 随机代币
                    events: [
                        {
                            name: 'SwapEvent',
                            data: {
                                amountIn: Math.floor(Math.random() * 1000000),
                                amountOut: Math.floor(Math.random() * 1000000),
                                user: `demo_user_${Math.random().toString(36).substr(2, 5)}`
                            }
                        }
                    ],
                    slot: Math.floor(Date.now() / 1000),
                    blockHeight: Math.floor(Date.now() / 1000) - 100000,
                    blockTime: Math.floor(Date.now() / 1000),
                    blockHash: `demo_block_${Math.random().toString(36).substr(2, 9)}`
                };

                await this.publishTransaction(demoTransaction);

                // 等待 3-8 秒再生成下一个交易
                const delay = 3000 + Math.random() * 5000;
                await new Promise(resolve => setTimeout(resolve, delay));

            } catch (error) {
                console.error('Demo simulation error:', error);
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
    }
}
