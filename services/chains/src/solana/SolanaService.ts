import { RedisService } from '../utils/RedisService';
import { PumpFunAMMService } from './dexs/pumpfun_amm/PumpFunAMMService';

export class SolanaService {
    private redisService: RedisService;
    private pumpFunAMMService: PumpFunAMMService;
    private isRunning: boolean = false;

    constructor(redisService: RedisService) {
        this.redisService = redisService;
        this.pumpFunAMMService = new PumpFunAMMService(redisService);
    }

    async start(): Promise<void> {
        if (this.isRunning) {
            console.log('SolanaService is already running');
            return;
        }

        try {
            console.log('🚀 Starting Solana Service...');

            // 启动 PumpFun AMM 服务
            await this.pumpFunAMMService.start();
            console.log('✅ PumpFun AMM Service started');

            this.isRunning = true;
            console.log('🎉 Solana Service started successfully');

        } catch (error) {
            console.error('❌ Failed to start Solana Service:', error);
            throw error;
        }
    }

    async stop(): Promise<void> {
        if (!this.isRunning) {
            return;
        }

        console.log('🛑 Stopping Solana Service...');
        
        try {
            await this.pumpFunAMMService.stop();
            
            this.isRunning = false;
            console.log('✅ Solana Service stopped');
        } catch (error) {
            console.error('❌ Error stopping Solana Service:', error);
            throw error;
        }
    }

    isServiceRunning(): boolean {
        return this.isRunning;
    }

    getPumpFunAMMService(): PumpFunAMMService {
        return this.pumpFunAMMService;
    }
}
