import { SolanaService } from './solana/SolanaService';
import { RedisService } from './utils/RedisService';
import { RedisServiceConfig, createRedisConfigFromEnv, autoLoadEnv } from '@mev-bot/shared';

export class ChainsService {
    private solanaService: SolanaService;
    private redisService: RedisService;
    private isRunning: boolean = false;
    private config: RedisServiceConfig;

    constructor(config?: RedisServiceConfig) {
        // 如果没有传入配置，则从环境变量创建
        if (!config) {
            autoLoadEnv(__dirname);
            this.config = createRedisConfigFromEnv();
        } else {
            this.config = config;
        }

        this.redisService = new RedisService(this.config);
        this.solanaService = new SolanaService(this.redisService);
    }

    async start(): Promise<void> {
        if (this.isRunning) {
            console.log('ChainsService is already running');
            return;
        }

        try {
            console.log('🚀 Starting Chains Service...');
            
            // 初始化 Redis 连接
            await this.redisService.init();
            console.log('✅ Redis Service initialized');

            // 启动 Solana 服务
            await this.solanaService.start();
            console.log('✅ Solana Service started');

            this.isRunning = true;
            console.log('🎉 Chains Service started successfully');

            // 监听进程退出信号
            process.on('SIGINT', () => this.stop());
            process.on('SIGTERM', () => this.stop());

        } catch (error) {
            console.error('❌ Failed to start Chains Service:', error);
            throw error;
        }
    }

    async stop(): Promise<void> {
        if (!this.isRunning) {
            return;
        }

        console.log('🛑 Stopping Chains Service...');
        
        try {
            await this.solanaService.stop();
            await this.redisService.disconnect();
            
            this.isRunning = false;
            console.log('✅ Chains Service stopped');
            process.exit(0);
        } catch (error) {
            console.error('❌ Error stopping Chains Service:', error);
            process.exit(1);
        }
    }

    isServiceRunning(): boolean {
        return this.isRunning;
    }
}
