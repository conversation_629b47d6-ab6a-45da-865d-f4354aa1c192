import { createClient, RedisClientType } from 'redis';
import { RedisServiceConfig, createRedisConfigFromEnv } from '@mev-bot/shared';

export class RedisService {
    private client: RedisClientType | null = null;
    private publisher: RedisClientType | null = null;
    private subscriber: RedisClientType | null = null;
    private isConnected: boolean = false;
    private config: RedisServiceConfig;

    constructor(config?: RedisServiceConfig) {
        // 如果没有传入配置，则从环境变量创建
        this.config = config || createRedisConfigFromEnv();

        // 构建 Redis URL
        const redisConfig = this.config.redis!;
        const auth = redisConfig.password ? `:${redisConfig.password}@` : '';
        const redisUrl = `redis://${auth}${redisConfig.host}:${redisConfig.port}/${redisConfig.db}`;

        this.client = createClient({ url: redisUrl });
        this.publisher = createClient({ url: redisUrl });
        this.subscriber = createClient({ url: redisUrl });

        // 错误处理
        if (this.config.enableLogging) {
            this.client.on('error', (err) => console.error('Redis Client Error:', err));
            this.publisher.on('error', (err) => console.error('Redis Publisher Error:', err));
            this.subscriber.on('error', (err) => console.error('Redis Subscriber Error:', err));
        }
    }

    async init(): Promise<void> {
        if (this.isConnected) {
            return;
        }

        try {
            await Promise.all([
                this.client?.connect(),
                this.publisher?.connect(),
                this.subscriber?.connect()
            ]);
            
            this.isConnected = true;
            console.log('Redis connections established');
        } catch (error) {
            console.error('Failed to connect to Redis:', error);
            throw error;
        }
    }

    async publish(channel: string, message: any): Promise<void> {
        if (!this.isConnected || !this.publisher) {
            console.warn('Redis not connected, skipping publish');
            return;
        }

        try {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
            await this.publisher.publish(channel, messageStr);
        } catch (error) {
            console.error('Failed to publish message:', error);
        }
    }

    async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
        if (!this.isConnected || !this.subscriber) {
            console.warn('Redis not connected, skipping subscribe');
            return;
        }

        try {
            await this.subscriber.subscribe(channel, callback);
        } catch (error) {
            console.error('Failed to subscribe to channel:', error);
        }
    }

    async set(key: string, value: any, ttl?: number): Promise<void> {
        if (!this.isConnected || !this.client) {
            console.warn('Redis not connected, skipping set');
            return;
        }

        try {
            const valueStr = typeof value === 'string' ? value : JSON.stringify(value);
            if (ttl) {
                await this.client.setEx(key, ttl, valueStr);
            } else {
                await this.client.set(key, valueStr);
            }
        } catch (error) {
            console.error('Failed to set key:', error);
        }
    }

    async get(key: string): Promise<string | null> {
        if (!this.isConnected || !this.client) {
            console.warn('Redis not connected, skipping get');
            return null;
        }

        try {
            return await this.client.get(key);
        } catch (error) {
            console.error('Failed to get key:', error);
            return null;
        }
    }

    async disconnect(): Promise<void> {
        if (!this.isConnected) {
            return;
        }

        try {
            await Promise.all([
                this.client?.disconnect(),
                this.publisher?.disconnect(),
                this.subscriber?.disconnect()
            ]);
            
            this.isConnected = false;
            console.log('Redis connections closed');
        } catch (error) {
            console.error('Error disconnecting from Redis:', error);
        }
    }

    getClient(): RedisClientType | null {
        return this.client;
    }

    isRedisConnected(): boolean {
        return this.isConnected;
    }
}
