import {
    DecisionScoreParam,
    getSingleRedisPubSubService,
    RedisPubSubService,
    RedisServiceConfig,
    createRedisConfigFromEnv,
    autoLoadEnv
} from "@mev-bot/shared";

/**
 * solana 决策服务
 */
export class SolanaDecisionService {

    private redisPubSubService!: RedisPubSubService;
    private config: RedisServiceConfig;

    constructor(config?: RedisServiceConfig) {
        // 如果没有传入配置，则从环境变量创建
        if (!config) {
            autoLoadEnv(__dirname);
            this.config = createRedisConfigFromEnv();
        } else {
            this.config = config;
        }
    }

    public async init() {
        this.redisPubSubService = getSingleRedisPubSubService(this.config);
        // todo 启动前初始化某些依赖
        console.log('SolanaDecisionService Init Success!')
    }

    public async doDecision() {
        // 分数事件
        this.redisPubSubService.subscribe('solana_decision', (decisionParam: DecisionScoreParam) => {
            console.log(decisionParam);
        });

        // 突发事件?
    }

}

// 如果直接运行此文件，启动服务
if (require.main === module) {
    const decisionService = new SolanaDecisionService();
    decisionService.init().then(async () => {
        console.log('🚀 Decision Service started successfully!');

        // 启动决策处理
        await decisionService.doDecision();

        console.log('🧠 Decision processing started, waiting for strategy events...');
    }).catch(console.error);
}