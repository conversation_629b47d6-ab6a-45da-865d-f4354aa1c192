{"name": "@mev-bot/gateway", "version": "1.0.0", "description": "MEV Bot Gateway Service", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/gateway-service.ts", "start": "node dist/src/gateway-service.js", "clean": "<PERSON><PERSON><PERSON> dist", "watch": "tsc --watch"}, "dependencies": {"@mev-bot/shared": "*", "axios": "^1.9.0", "dotenv": "^16.4.5", "fast-event-emitter": "^0.0.2", "level": "^7.0.1"}, "devDependencies": {"@solana/web3.js": "^1.98.0", "@types/dotenv": "^8.2.0", "@types/google-protobuf": "^3.15.12", "@types/node": "^18.19.75", "rimraf": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}