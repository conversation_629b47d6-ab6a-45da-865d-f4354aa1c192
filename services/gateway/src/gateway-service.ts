import {
    STABLE_COINS,
    getSingleRedisPubSubService,
    RedisPubSubService,
    MessageData,
    PoolSwapInfo,
    SwapEvent,
    SwapModel,
    RedisServiceConfig
} from "@mev-bot/shared";
import * as fs from "node:fs";
import path from "path";
import { getGatewayRedisConfig } from "../redis-config";
import {Logger} from "@mev-bot/shared/dist/utils/logger";

/**
 * Solana 统一接入模型转换
 */
export class GatewayService {
    private redisPubSubService: RedisPubSubService;
    private config: RedisServiceConfig;

    // 构造函数初始化配置
    constructor(config?: RedisServiceConfig) {
        this.config = config || getGatewayRedisConfig();
        this.redisPubSubService = getSingleRedisPubSubService(this.config);
    }

    // 初始化 Redis 服务
    public async init(): Promise<void> {
        this.redisPubSubService = await getSingleRedisPubSubService(this.config);
        Logger.info('RedisPubSubService initialized successfully');
    }

    // 测试方法：读取 testData.json 并处理
    public async startTest(callback: (swapModel: SwapModel) => void): Promise<void> {
        const filePath = path.join(__dirname, 'testData.json');
        return new Promise((resolve, reject) => {
            fs.readFile(filePath, 'utf8', async (err, data) => {
                if (err) {
                    Logger.error('Failed to read test data:', err);
                    return reject(err);
                }
                try {
                    let swapModels: SwapModel[] = JSON.parse(data);
                    for (const swapModel of swapModels) {
                        await callback(swapModel);
                    }
                    resolve();
                } catch (error) {
                    Logger.error('Failed to parse test data:', error);
                    reject(error);
                }
            });
        });
    }

    // 启动 Redis 订阅
    public async start(callback: (result: SwapModel) => Promise<void>): Promise<void> {
        await this.redisPubSubService.subscribe<MessageData>('mmg-data-report', async (data) => {
            if (data.topic === 'topic_sol-swap-event-v3') {
                try {
                    let swapEvent: SwapEvent = JSON.parse(data.message);
                    let pumpAmmPoolSwaps = swapEvent.pumpAmmPoolSwaps;
                    if (pumpAmmPoolSwaps && pumpAmmPoolSwaps.length > 0) {
                        await this.fillSwapModels(swapEvent, pumpAmmPoolSwaps, callback);
                    }
                } catch (error) {
                    Logger.error('Failed to process SwapEvent:', error);
                }
            }
        });
    }

    // 发布 SwapModel 到 Redis
    public async publishToRedis(swapModel: SwapModel): Promise<void> {
        try {
            await this.redisPubSubService.publish('swap-model-channel', JSON.stringify(swapModel));
            Logger.info(`Published swapModel to Redis: ${swapModel.signature}`);
        } catch (error) {
            Logger.error('Failed to publish to Redis:', error);
            throw error;
        }
    }


    // 处理 SwapModel 逻辑
    private async fillSwapModels(swapEvent: SwapEvent, pools: PoolSwapInfo[], callback: (result: SwapModel) => Promise<void>) {
        const { user, signatures, slot, timestamp: blockTime } = swapEvent;
        const systemTime = new Date().getTime() / 1000;
        const diffTime = blockTime - systemTime;

        for (const swap of pools) {
            try {
                let poolMarketInfo = swap.marketInfo;
                if (!poolMarketInfo) continue;

                let pool = swap.poolAddress;
                if (!pool || pool === '') continue;

                let usdTokenPrice = poolMarketInfo.usdTokenPrice;
                if (!usdTokenPrice) continue;

                let marketCap = poolMarketInfo.totalUsdValue;
                if (!marketCap) continue;

                let anchorLiquidity = poolMarketInfo.poolAnchorBalance * poolMarketInfo.currentAnchorUsdPrice;
                let tokenLiquidity = poolMarketInfo.poolTokenBalance * usdTokenPrice;
                let totalLiquidity = anchorLiquidity + tokenLiquidity;
                let mint = STABLE_COINS.includes(swap.tokenInMint) ? swap.tokenOutMint : swap.tokenInMint;
                let isBuy = STABLE_COINS.includes(swap.tokenInMint);
                let amount = STABLE_COINS.includes(swap.tokenInMint) ? swap.tokenOutAmount : swap.tokenInAmount;
                let tokenSymbol = swap.marketInfo?.tokenSymbol || '';
                let totalUsdValue = usdTokenPrice * amount;
                let dexType = swap.type;

                const swapModel: SwapModel = {
                    chainType: 'solana',
                    mint: mint,
                    pool: pool,
                    symbol: tokenSymbol,
                    isBuy: isBuy,
                    amount: amount,
                    usdPrice: usdTokenPrice,
                    swapUsdPrice: totalUsdValue,
                    marketCap: marketCap,
                    anchorUsdLiquidity: anchorLiquidity,
                    tokenUsdLiquidity: tokenLiquidity,
                    totalUsdLiquidity: totalLiquidity,
                    currentAnchorUsdPrice: poolMarketInfo.currentAnchorUsdPrice,
                    user: user,
                    blockTime: blockTime,
                    block: slot,
                    signature: signatures,
                    diffTime: diffTime,
                    dexType: dexType
                };

                await callback(swapModel);
            } catch (error) {
                Logger.error(`Error processing swap: ${error instanceof Error ? error.message : error}`);
            }
        }
    }
}

// 主程序
if (require.main === module) {
    async function main() {
        try {
            // 创建 GatewayService 实例
            const gatewayService = new GatewayService();
            await gatewayService.init();
            Logger.info('🚀 Gateway Service started successfully!');

            // 启动测试模式
            await gatewayService.startTest(async (swapModel) => {
                Logger.info('📡 Received swap event:', swapModel);

                // 发布到 Redis
                gatewayService.publishToRedis(swapModel);
            });

            Logger.info('🌐 Test data processing completed.');

            // 可选：如果只需要处理测试数据后退出
            //process.exit(0);
        } catch (error) {
            Logger.error('Error in main:', error);
            process.exit(1);
        }
    }

    // 捕获退出信号以清理资源
    process.on('SIGINT', async () => {
        Logger.info('🛑 Shutting down Gateway Service...');
        try {
            console.log('🛑 Gateway Service stopped successfully.');
            process.exit(0);
        } catch (error) {
            console.error('Error during shutdown:', error);
            process.exit(1);
        }
    });
    main();
}