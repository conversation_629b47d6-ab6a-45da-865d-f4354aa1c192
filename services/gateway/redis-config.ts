import { 
    RedisServiceConfig, 
    createRedisConfigFromEnv,
    autoLoadEnv,
    createDevRedisConfig 
} from '@mev-bot/shared';
import * as path from 'path';

/**
 * Gateway 服务的 Redis 配置管理
 */
export class GatewayRedisConfig {
    /**
     * 获取 Gateway 服务的 Redis 配置
     */
    public static getConfig(): RedisServiceConfig {
        // 方案 1: 自动查找并加载 .env 文件
        autoLoadEnv(__dirname);
        
        // 方案 2: 手动指定 .env 文件路径
        // const envPath = path.join(__dirname, '.env');
        // return createRedisConfigFromEnv(process.env.NODE_ENV === 'development', envPath);
        
        // 方案 3: 使用系统环境变量
        return createRedisConfigFromEnv();
    }

    /**
     * 获取自定义配置（如果需要 Gateway 特殊配置）
     */
    public static getCustomConfig(): RedisServiceConfig {
        return {
            redis: {
                host: process.env.GATEWAY_REDIS_HOST || '127.0.0.1',
                port: parseInt(process.env.GATEWAY_REDIS_PORT || '6379'),
                password: process.env.GATEWAY_REDIS_PASSWORD || undefined,
                db: parseInt(process.env.GATEWAY_REDIS_DB || '1'), // 使用不同的数据库
            },
            isDev: process.env.NODE_ENV === 'development',
            enableLogging: process.env.GATEWAY_REDIS_LOGGING === 'true',
        };
    }
}

// 导出便捷函数
export const getGatewayRedisConfig = () => GatewayRedisConfig.getConfig();
