import {BaseSimpleStrategy} from "./simple/base-simple-strategy";
import {PriceTrendSimpleStrategy} from "./simple/price-trend-simple-strategy";
import {SocializingSimpleStrategy} from "./simple/socializing-simple-strategy";
import {TradingVolumeSimpleStrategy} from "./simple/trading-volume-simple-strategy";
import {
    DecisionScoreParam,
    StrategyScoreResult,
    getRedisPubSubService,
    getSingleRedisPubSubService,
    RedisPubSubService,
    SwapModel,
    RedisServiceConfig,
    createRedisConfigFromEnv,
    autoLoadEnv
} from "@mev-bot/shared";
import {GatewayService} from "@mev-bot/gateway";



export class StrategyProcessor {
    private strategyList: BaseSimpleStrategy[] = [];
    private redisPubSubService!: RedisPubSubService;
    private config: RedisServiceConfig;

    constructor(config?: RedisServiceConfig) {
        // 如果没有传入配置，则从环境变量创建
        if (!config) {
            autoLoadEnv(__dirname);
            this.config = createRedisConfigFromEnv();
        } else {
            this.config = config;
        }
    }

    public async init() {
        // 初始化redis
        this.redisPubSubService = getRedisPubSubService(this.config);
        // 初始化策略组
        this.strategyList.push(new PriceTrendSimpleStrategy()); // 价格趋势
        this.strategyList.push(new SocializingSimpleStrategy()); // 社交影响
        this.strategyList.push(new TradingVolumeSimpleStrategy()); // 交易量
    }

    /**
     * 开始策略处理
     */
    public async doProcessor() {
        await this.redisPubSubService.subscribe('swap-model-channel', async (swapModel: SwapModel) => {
            try {
                // todo 这里其实可以去异步回调做相关策略
                this.executeAndBuildStrategyScores(swapModel).catch((e) => {
                    console.error(e);
                });

            } catch (e) {
                console.error(e);
            }
        });
    }

    /**
     * 并行执行策略并构建分数结果，带超时机制（非阻塞）
     * @param swapModel SwapModel 数据
     */
    private async executeAndBuildStrategyScores(swapModel: SwapModel): Promise<void> {
        const timeout = (ms: number) => new Promise((_, reject) => setTimeout(() => reject(new Error(`Timeout after ${ms}ms`)), ms));

        // 并行执行所有策略
        const strategyPromises = this.strategyList.map(async (s) => {
            try {
                // 设置策略超时
                const result = await Promise.race([
                    s.analyze(swapModel),
                    timeout(200)
                ]) as StrategyScoreResult; // 类型断言
                return result;
            } catch (e) {
                console.error(`Strategy ${s.getStrategyType()} failed:`, e);
                return { strategyType: s.getStrategyType(), buyScore: 0, sellScore: 0 };
            }
        });

        // 不等待 Promise.all 完成，直接异步处理结果
        Promise.all(strategyPromises)
            .then((strategyScoreResults) => {
                // 异步构建决策参数
                const decisionParam = this.buildDecisionParam(strategyScoreResults);
                // 异步发布决策参数
                this.redisPubSubService.publish('solana_decision', decisionParam).catch((e) => {
                    console.error(`Failed to publish decision: ${e}`);
                });
            })
            .catch((e) => {
                console.error(`One or more strategies failed: ${e}`);
            });
    }

    /**
     * 构建决策参数
     */
    private buildDecisionParam(strategyScoreResults: StrategyScoreResult[]): DecisionScoreParam {
        return strategyScoreResults.reduce((acc, cur) => ({
            ...acc,
            [cur.strategyType]: cur
        }), {} as DecisionScoreParam);
    }
}

if (require.main === module) {
    const strategyProcessor = new StrategyProcessor();
    strategyProcessor.init().then(async () => {
        console.log('🚀 Strategy Processor started successfully!');
        // 启动策略处理
        await strategyProcessor.doProcessor();
        console.log('📈 Strategy processing started, waiting for swap events...');
    }).catch(console.error);
}