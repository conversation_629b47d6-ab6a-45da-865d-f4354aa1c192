import {BaseSimpleStrategy} from "./base-simple-strategy";
import {SwapModel, StrategyScoreResult, StrategyType, TRADING_VOLUME} from "@mev-bot/shared";

/**
 * 交易量策略
 */
export class TradingVolumeSimpleStrategy implements BaseSimpleStrategy {


    public readonly weight = 0.25;


    public async analyze(swapModel: SwapModel): Promise<StrategyScoreResult> {
        let result: StrategyScoreResult = {
            strategyType: this.getStrategyType(),
            buyScore: 0,
            sellScore: 0
        }
        return Promise.resolve(result);
    }

    public async getBuyScore(): Promise<number> {
        return Promise.resolve(0);
    }

    public async getSellScore(): Promise<number> {
        return Promise.resolve(0);
    }


    public getStrategyType(): string {
        return TRADING_VOLUME;
    }
}