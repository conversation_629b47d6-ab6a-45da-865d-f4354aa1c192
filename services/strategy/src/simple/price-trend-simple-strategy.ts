import {BaseSimpleStrategy} from "./base-simple-strategy";
import {SwapModel, StrategyScoreResult, StrategyType, PRICE_TREND} from "@mev-bot/shared";

/**
 * 价格趋势策略 todo 应该是有一个滑动的时间窗口
 */
export class PriceTrendSimpleStrategy implements BaseSimpleStrategy {


    public readonly weight = 0.5;


    public async analyze(swapModel: SwapModel): Promise<StrategyScoreResult> {
        let result: StrategyScoreResult = {
            strategyType: this.getStrategyType(),
            buyScore: 0,
            sellScore: 0
        }
        return Promise.resolve(result);
    }

    public async getBuyScore(): Promise<number> {
        return Promise.resolve(0);
    }

    public async getSellScore(): Promise<number> {
        return Promise.resolve(0);
    }

    public getStrategyType(): string {
        return PRICE_TREND;
    }


}