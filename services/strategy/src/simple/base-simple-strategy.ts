import {SwapModel, StrategyScoreResult, StrategyType} from "@mev-bot/shared";

export interface BaseSimpleStrategy {


    /**
     * 权重因子, 0-1之间
     */
    readonly weight: number;

    /**
     * 获取买入分数
     */
    getBuyScore(): Promise<number>;

    /**
     * 获取卖出分数
     */
    getSellScore(): Promise<number>;

    /**
     * 分析交易
     * @param swapModel
     */
    analyze(swapModel: SwapModel): Promise<StrategyScoreResult>;

    /**
     * 获取处理策略类型
     */
    getStrategyType(): string;

}