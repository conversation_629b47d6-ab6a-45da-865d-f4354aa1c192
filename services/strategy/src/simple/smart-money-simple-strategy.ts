import {BaseSimpleStrategy} from "./base-simple-strategy";
import {SwapModel, StrategyScoreResult, StrategyType, SMART_MONEY} from "@mev-bot/shared";

/**
 * 聪明钱策略 todo 聪明钱需要统计胜率, 目前可以暂时手动统计? 胜率做一个权重值
 */
export class SmartMoneySimpleStrategy implements BaseSimpleStrategy {

    public readonly weight = 0.25;


    public async analyze(swapModel: SwapModel): Promise<StrategyScoreResult> {
        let result: StrategyScoreResult = {
            strategyType: this.getStrategyType(),
            buyScore: 0,
            sellScore: 0
        }
        return Promise.resolve(result);
    }

    public async getBuyScore(): Promise<number> {
        return Promise.resolve(0);
    }

    public async getSellScore(): Promise<number> {
        return Promise.resolve(0);
    }

    public getStrategyType(): string {
        return SMART_MONEY;
    }

}