// import {BaseSimpleStrategy} from "./simple/base-simple-strategy";
// import {PriceTrendSimpleStrategy} from "./simple/price-trend-simple-strategy";
// import {SmartMoneySimpleStrategy} from "./simple/smart-money-simple-strategy";
// import {SocializingSimpleStrategy} from "./simple/socializing-simple-strategy";
// import {TradingVolumeSimpleStrategy} from "./simple/trading-volume-simple-strategy";
// import {SolanaPortService} from "../port/port-service";
// import EventEmitter from "node:events";
// import {DecisionScoreParam, StrategyScoreResult} from "../types/types";
// import {StrategyType} from "../constants/constant";
// import {SwapModel} from "../port/model/model-types";
//
// export class StrategyProcessor extends EventEmitter {
//     private strategyList: BaseSimpleStrategy[] = [];
//
//     constructor() {
//         super();
//         // 初始化策略组
//         this.strategyList.push(new PriceTrendSimpleStrategy()); // 价格趋势
//         //this.strategyList.push(new SmartMoneySimpleStrategy()); // 聪明钱动态
//         this.strategyList.push(new SocializingSimpleStrategy()); // 社交影响
//         this.strategyList.push(new TradingVolumeSimpleStrategy()); // 交易量
//     }
//
//
//     /**
//      * 开始策略处理
//      */
//     public async doProcessor() {
//         await (await SolanaPortService.init()).start(async (swapModel) => {
//             // 1. 策略并行
//             const strategyScoreResults = await this.executeAndBuildStrategyScores(swapModel);
//
//             // 2. 构建决策参数
//             const decisionParam = this.buildDecisionParam(strategyScoreResults);
//
//             // 3. 提交决策参数
//             this.emit("decision", decisionParam);
//
//             console.info(swapModel);
//         });
//     }
//
//
//     private async executeAndBuildStrategyScores(swapModel: SwapModel): Promise<StrategyScoreResult[]> {
//         return await Promise.all(
//             this.strategyList.map(s => {
//                 try {
//                     return s.analyze(swapModel);
//                 } catch (e) {
//                     console.error(e);
//                     return Promise.resolve({strategyType: s.getStrategyType(), buyScore: 0, sellScore: 0});
//                 }
//             })
//         );
//     }
//
//     /**
//      * 构建决策参数
//      * @param strategyScoreResults
//      * @private
//      */
//     private buildDecisionParam(strategyScoreResults: StrategyScoreResult[]): DecisionScoreParam {
//         return strategyScoreResults.reduce((acc, cur) => ({
//             ...acc,
//             [cur.strategyType]: cur
//         }), {} as DecisionScoreParam);
//     }
//     // private buildDecisionParam(strategyScoreResults: StrategyScoreResult[]): DecisionScoreParam {
//     //     const decisionModel = new DecisionScoreParam();
//     //     strategyScoreResults.forEach(s => {
//     //         let strategyType = s.strategyType;
//     //         switch (strategyType) {
//     //             case StrategyType.PRICE_TREND:
//     //                 decisionModel.priceTrend = s;
//     //                 break;
//     //             case StrategyType.SMART_MONEY:
//     //                 decisionModel.smartMoney = s;
//     //                 break;
//     //             case StrategyType.SOCIALIZING_INFORMATION:
//     //                 break;
//     //             case StrategyType.TRADING_VOLUME:
//     //                 decisionModel.tradingVolume = s;
//     //                 break;
//     //             default:
//     //                 break;
//     //         }
//     //     });
//     //     return decisionModel;
//     // }
// }
//
// async function test() {
//     let strategyHandle = new StrategyProcessor();
//     let promise = await strategyHandle.doProcessor();
// }
//
// test();