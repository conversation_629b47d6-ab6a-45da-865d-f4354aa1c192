{"name": "@mev-bot/strategy", "version": "1.0.0", "description": "MEV Bot Strategy Service", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/strategy-processor.ts", "start": "node dist/src/strategy-processor.js", "clean": "<PERSON><PERSON><PERSON> dist", "watch": "tsc --watch"}, "dependencies": {"@mev-bot/shared": "*", "@mev-bot/gateway": "*"}, "devDependencies": {"@types/node": "^18.19.75", "rimraf": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}