import {
    getSingleRedisPubSubService,
    RedisPubSubService,
    SwapModel,
    RedisServiceConfig,
    createRedisConfigFromEnv,
    autoLoadEnv
} from "@mev-bot/shared";

/**
 * 订单状态枚举
 */
export enum OrderStatus {
    PENDING = 'PENDING',
    FILLED = 'FILLED',
    CANCELLED = 'CANCELLED',
    FAILED = 'FAILED'
}

/**
 * 订单类型枚举
 */
export enum OrderType {
    BUY = 'BUY',
    SELL = 'SELL'
}

/**
 * 订单接口
 */
export interface Order {
    id: string;
    symbol: string;
    type: OrderType;
    amount: number;
    price: number;
    status: OrderStatus;
    createdAt: number;
    updatedAt: number;
    signature?: string;
    error?: string;
}

/**
 * MEV Bot 订单管理服务
 */
export class OrdersService {
    private redisPubSubService!: RedisPubSubService;
    private orders: Map<string, Order> = new Map();
    private config: RedisServiceConfig;

    constructor(config?: RedisServiceConfig) {
        // 如果没有传入配置，则从环境变量创建
        if (!config) {
            autoLoadEnv(__dirname);
            this.config = createRedisConfigFromEnv();
        } else {
            this.config = config;
        }
    }

    public async init() {
        this.redisPubSubService = getSingleRedisPubSubService(this.config);
        console.log('OrdersService Init Success!');

        // 订阅交易完成事件
        await this.subscribeToTradeEvents();
    }

    /**
     * 创建新订单
     */
    public async createOrder(symbol: string, type: OrderType, amount: number, price: number): Promise<Order> {
        const order: Order = {
            id: this.generateOrderId(),
            symbol,
            type,
            amount,
            price,
            status: OrderStatus.PENDING,
            createdAt: Date.now(),
            updatedAt: Date.now()
        };

        this.orders.set(order.id, order);
        
        console.log(`📋 Created ${type} order for ${symbol}:`, {
            id: order.id,
            amount: order.amount,
            price: order.price
        });

        // 发布订单创建事件
        await this.publishOrderEvent('order-created', order);
        
        return order;
    }

    /**
     * 更新订单状态
     */
    public async updateOrderStatus(orderId: string, status: OrderStatus, signature?: string, error?: string): Promise<Order | null> {
        const order = this.orders.get(orderId);
        if (!order) {
            console.error(`Order ${orderId} not found`);
            return null;
        }

        order.status = status;
        order.updatedAt = Date.now();
        
        if (signature) {
            order.signature = signature;
        }
        
        if (error) {
            order.error = error;
        }

        this.orders.set(orderId, order);

        console.log(`📋 Updated order ${orderId} status to ${status}`);

        // 发布订单更新事件
        await this.publishOrderEvent('order-updated', order);

        return order;
    }

    /**
     * 获取订单
     */
    public getOrder(orderId: string): Order | null {
        return this.orders.get(orderId) || null;
    }

    /**
     * 获取所有订单
     */
    public getAllOrders(): Order[] {
        return Array.from(this.orders.values());
    }

    /**
     * 获取特定状态的订单
     */
    public getOrdersByStatus(status: OrderStatus): Order[] {
        return Array.from(this.orders.values()).filter(order => order.status === status);
    }

    /**
     * 获取特定代币的订单
     */
    public getOrdersBySymbol(symbol: string): Order[] {
        return Array.from(this.orders.values()).filter(order => order.symbol === symbol);
    }

    /**
     * 取消订单
     */
    public async cancelOrder(orderId: string): Promise<boolean> {
        const order = this.orders.get(orderId);
        if (!order) {
            console.error(`Order ${orderId} not found`);
            return false;
        }

        if (order.status !== OrderStatus.PENDING) {
            console.error(`Cannot cancel order ${orderId} with status ${order.status}`);
            return false;
        }

        await this.updateOrderStatus(orderId, OrderStatus.CANCELLED);
        return true;
    }

    /**
     * 订阅交易事件
     */
    private async subscribeToTradeEvents(): Promise<void> {
        try {
            // 订阅交易完成事件
            await this.redisPubSubService.subscribe('trade-completed', (data: any) => {
                this.handleTradeCompleted(data);
            });

            // 订阅交易失败事件
            await this.redisPubSubService.subscribe('trade-failed', (data: any) => {
                this.handleTradeFailed(data);
            });
        } catch (error) {
            console.error('Failed to subscribe to trade events:', error);
        }
    }

    /**
     * 处理交易完成事件
     */
    private async handleTradeCompleted(data: any): Promise<void> {
        const { orderId, signature } = data;
        if (orderId) {
            await this.updateOrderStatus(orderId, OrderStatus.FILLED, signature);
        }
    }

    /**
     * 处理交易失败事件
     */
    private async handleTradeFailed(data: any): Promise<void> {
        const { orderId, error } = data;
        if (orderId) {
            await this.updateOrderStatus(orderId, OrderStatus.FAILED, undefined, error);
        }
    }

    /**
     * 发布订单事件
     */
    private async publishOrderEvent(eventType: string, order: Order): Promise<void> {
        try {
            await this.redisPubSubService.publish(eventType, {
                orderId: order.id,
                symbol: order.symbol,
                type: order.type,
                status: order.status,
                timestamp: Date.now()
            });
        } catch (error) {
            console.error(`Failed to publish ${eventType} event:`, error);
        }
    }

    /**
     * 生成订单ID
     */
    private generateOrderId(): string {
        return `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取订单统计信息
     */
    public getOrderStats(): {
        total: number;
        pending: number;
        filled: number;
        cancelled: number;
        failed: number;
    } {
        const orders = this.getAllOrders();
        return {
            total: orders.length,
            pending: orders.filter(o => o.status === OrderStatus.PENDING).length,
            filled: orders.filter(o => o.status === OrderStatus.FILLED).length,
            cancelled: orders.filter(o => o.status === OrderStatus.CANCELLED).length,
            failed: orders.filter(o => o.status === OrderStatus.FAILED).length
        };
    }
}

// 如果直接运行此文件，启动服务
if (require.main === module) {
    const ordersService = new OrdersService();
    ordersService.init().then(() => {
        console.log('🚀 Orders Service started successfully!');

        // 演示创建一些测试订单
        setTimeout(async () => {
            await ordersService.createOrder('BUFFALO', OrderType.BUY, 1000, 0.0001);
            await ordersService.createOrder('GTA', OrderType.SELL, 500, 0.003);

            console.log('📊 Order Stats:', ordersService.getOrderStats());
        }, 2000);
    }).catch(console.error);
}
